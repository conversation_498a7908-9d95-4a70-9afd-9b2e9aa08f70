/**
 * Firebase initialization module
 * This module ensures Firebase is properly initialized before use
 */

import { app, auth, functions, db } from './firebase';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Add debug logs
console.log('firebaseInit imported, auth available:', !!auth);
console.log('firebaseInit imported, app available:', !!app);

/**
 * Initialize Firebase services
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise} - Resolves when Firebase is initialized or timeout is reached
 */
export const initializeFirebase = (timeout = 5000) => {
  return new Promise((resolve) => {
    console.log('initializeFirebase called with timeout:', timeout);

    // Set a timeout to ensure we don't hang
    const timeoutId = setTimeout(() => {
      console.warn(`Firebase initialization timed out after ${timeout}ms`);
      resolve({
        initialized: false,
        error: new Error(`Initialization timed out after ${timeout}ms`),
        timedOut: true
      });
    }, timeout);

    try {
      console.log('Starting Firebase initialization');

      // Check if Firebase app is initialized
      if (!app) {
        console.error('Firebase app is not initialized');
        clearTimeout(timeoutId);
        resolve({
          initialized: false,
          error: new Error('Firebase app is not initialized')
        });
        return;
      }

      console.log('Firebase app is initialized');

      // We'll consider initialization successful even if some services fail
      // This allows the app to function in a degraded state
      let authInitialized = false;
      let dbInitialized = false;

      // Verify auth is working
      try {
        console.log('Checking auth...');
        if (auth) {
          console.log('Auth is already initialized');
          authInitialized = true;
        } else {
          console.log('Trying to get auth instance');
          const authInstance = getAuth(app);
          console.log('Got auth instance:', !!authInstance);
          authInitialized = !!authInstance;
        }
      } catch (authError) {
        console.error('Auth initialization error:', authError);
        // Continue even if auth fails
      }

      // Verify Firestore is working
      try {
        console.log('Checking Firestore...');
        if (db) {
          console.log('Firestore is already initialized');
          dbInitialized = true;
        } else {
          console.log('Trying to get Firestore instance');
          const dbInstance = getFirestore(app);
          console.log('Got Firestore instance:', !!dbInstance);
          dbInitialized = !!dbInstance;
        }
      } catch (dbError) {
        console.error('Firestore initialization error:', dbError);
        // Continue even if Firestore fails
      }

      // Clear the timeout since initialization completed
      clearTimeout(timeoutId);
      console.log('Firebase initialization completed with status:', {
        authInitialized,
        dbInitialized
      });

      resolve({
        initialized: true,
        authInitialized,
        dbInitialized,
        app: app,
        auth: auth,
        db: db,
        functions: functions
      });

    } catch (error) {
      // Clear the timeout since we're resolving now
      clearTimeout(timeoutId);
      console.error('Firebase initialization error:', error);
      resolve({
        error,
        initialized: false
      });
    }
  });
};
