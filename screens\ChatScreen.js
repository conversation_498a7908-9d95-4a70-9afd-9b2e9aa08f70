/**
 * AI Chatbot Screen for Smart AsthmaCare
 * Uses react-native-gifted-chat for the UI
 * Connects to OpenRouter.ai API for AI responses
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, ActivityIndicator, Text, KeyboardAvoidingView, Platform, BackHandler, Keyboard, TouchableWithoutFeedback, TouchableOpacity } from 'react-native';
import { GiftedChat, Bubble, InputToolbar } from 'react-native-gifted-chat';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../context/SupabaseAuthContext';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import i18n from '../i18n/i18n';

// Import OpenRouter service
import { sendMessageToOpenRouter, analyzePeakFlow } from '../services/openRouterService';

const ChatScreen = ({ navigation }) => {
  // Get user data from auth context at the component level
  const { userProfile, currentUser } = useAuth();
  const userName = userProfile?.name || currentUser?.email?.split('@')[0] || 'there';

  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(true); // Assume online by default
  const [peakFlowData, setPeakFlowData] = useState(null);
  const [medicationData, setMedicationData] = useState(null);

  // Reference to track if component is mounted
  const isMounted = useRef(true);

  // Reference to track the current input text
  const inputTextRef = useRef('');

  // Reference to the GiftedChat component
  const chatRef = useRef();

  // State to track keyboard visibility (used in keyboard listeners)
  const [, setKeyboardVisible] = useState(false);

  // State to track when to clear the input
  const [clearInput, setClearInput] = useState(false);

  // State to track current input text
  const [inputText, setInputText] = useState('');

  // Helper function to create a bot message with guaranteed unique ID
  const createBotMessage = (text) => {
    return {
      _id: `bot-${Date.now()}-${Math.round(Math.random() * 1000000)}`,
      text: text,
      createdAt: new Date(),
      user: {
        _id: 2,
        name: 'mHealth Assistant',
        avatar: 'https://placehold.co/100x100/3A5A40/FFFFFF?text=AI',
      },
    };
  };

  // Load chat history and user data on mount
  useEffect(() => {
    console.log('ChatScreen mounted');

    // Initialize chat asynchronously
    const initChat = async () => {
      try {
        // First check if we have saved messages
        const savedMessages = await AsyncStorage.getItem('chatMessages');
        let hasMessages = false;

        if (savedMessages) {
          try {
            const parsedMessages = JSON.parse(savedMessages);
            if (parsedMessages && parsedMessages.length > 0) {
              console.log('Found saved messages:', parsedMessages.length);
              setMessages(parsedMessages);
              hasMessages = true;
            }
          } catch (parseError) {
            console.error('Error parsing saved messages:', parseError);
          }
        }

        // If no messages were found, send welcome message
        if (!hasMessages) {
          console.log('No saved messages found, sending welcome message');
          // Add a small delay to ensure component is fully mounted
          setTimeout(() => {
            sendWelcomeMessage();
          }, 300);
        }

        // Load user data
        await loadUserData();

        // Check if device is online
        await checkNetworkStatus();
      } catch (error) {
        console.error('Error initializing chat:', error);
        // Ensure welcome message is sent even if there's an error
        sendWelcomeMessage();
      }
    };

    // Start the async operations
    initChat();

    // Set up keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      // Clean up listeners and refs
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
      isMounted.current = false;
    };
  }, []);

  // Also add a useFocusEffect to ensure welcome message shows when screen is focused
  useFocusEffect(
    useCallback(() => {
      // Check if messages are empty and send welcome message if needed
      if (messages.length === 0) {
        console.log('Screen focused with no messages, sending welcome message');
        sendWelcomeMessage();
      }
    }, [messages])
  );

  // Handle back button press with useFocusEffect
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Just let the default back behavior happen
        return false;
      };

      // For React Navigation 7, we need to use a different approach
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        // Clean up the event listener when the screen loses focus
        subscription.remove();
      };
    }, [])
  );

  // Check if device has internet connection
  const checkNetworkStatus = async () => {
    try {
      // Check general internet connectivity
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        timeout: 5000
      });

      setIsOnline(response.ok);
      if (!response.ok) {
        setError('Network connectivity issue. Chat will work in offline mode only.');
      } else {
        setError(null);
      }
    } catch (error) {
      setIsOnline(false);
      console.warn('Network connectivity issue:', error.message);
      setError('Network connectivity issue. Chat will work in offline mode only.');
    }
  };

  // Load user's peak flow and medication data
  const loadUserData = async () => {
    try {
      // Load peak flow data
      const peakFlowJson = await AsyncStorage.getItem('symptomChartData');
      if (peakFlowJson) {
        setPeakFlowData(JSON.parse(peakFlowJson));
      }

      // Load medication data if available
      const medicationJson = await AsyncStorage.getItem('medicationData');
      if (medicationJson) {
        setMedicationData(JSON.parse(medicationJson));
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  // This function is now handled directly in the useEffect hook
  // Keeping it as a comment for reference
  /*
  const loadMessages = async () => {
    try {
      const savedMessages = await AsyncStorage.getItem('chatMessages');
      if (savedMessages) {
        const parsedMessages = JSON.parse(savedMessages);
        if (parsedMessages && parsedMessages.length > 0) {
          setMessages(parsedMessages);
          return; // Successfully loaded messages, no need for welcome message
        }
      }

      // If we get here, either no messages were found or the array was empty
      sendWelcomeMessage();
    } catch (error) {
      console.error('Error loading messages:', error);
      // Send welcome message as fallback
      sendWelcomeMessage();
    }
  };
  */

  // Save messages to AsyncStorage with retry mechanism
  const saveMessages = async (newMessages) => {
    try {
      // First, stringify the messages
      const messagesString = JSON.stringify(newMessages);

      // Try to save messages up to 3 times
      let saved = false;
      let attempts = 0;

      while (!saved && attempts < 3) {
        try {
          await AsyncStorage.setItem('chatMessages', messagesString);
          console.log('Messages saved successfully, attempt:', attempts + 1);
          saved = true;
        } catch (saveError) {
          attempts++;
          console.error(`Error saving messages (attempt ${attempts}/3):`, saveError);
          // Wait a short time before retrying
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Verify the save was successful by reading back
      if (saved) {
        const savedMessages = await AsyncStorage.getItem('chatMessages');
        if (savedMessages) {
          console.log('Verified messages were saved correctly');
        } else {
          console.error('Failed to verify saved messages');
        }
      }
    } catch (error) {
      console.error('Error in saveMessages function:', error);
    }
  };

  // Send welcome message from the bot
  const sendWelcomeMessage = async () => {
    try {
      console.log('Sending welcome message');

      // Create a simple, personalized welcome message using the userName from component level
      const welcomeText = `Hello ${userName}! How can I assist you today?`;

      // Create a bot message with a guaranteed unique ID
      const welcomeMessage = createBotMessage(welcomeText);

      console.log('Setting welcome message:', welcomeMessage);

      // Force a delay to ensure UI updates properly
      await new Promise(resolve => setTimeout(resolve, 100));

      // Set messages and save them
      setMessages([welcomeMessage]);
      await saveMessages([welcomeMessage]);

      // Verify the welcome message was set
      console.log('Welcome message set, current messages:', messages);
    } catch (error) {
      console.error('Error sending welcome message:', error);
    }
  };

  // Handle sending messages
  const onSend = useCallback(async (newMessages = []) => {
    try {
      // Create a user message with a guaranteed unique ID
      const userMessage = {
        _id: `user-${Date.now()}`, // Guaranteed unique ID
        text: newMessages[0].text,
        createdAt: new Date(),
        user: {
          _id: 1,
        },
        sent: true,
        received: true,
      };

      // Add the user message to the chat immediately
      const updatedMessages = GiftedChat.append(messages, [userMessage]);
      setMessages(updatedMessages);

      // Save messages first to ensure they persist
      await saveMessages(updatedMessages);
      console.log('User message saved before processing');

      // Then process the message to get AI response
      processMessage(newMessages[0].text, updatedMessages);
    } catch (error) {
      console.error('Error in onSend:', error);
    }
  }, [messages]);

  // Process user message and get response
  const processMessage = async (text, currentMessages = messages) => {
    console.log('Processing message:', text);
    console.log('Current messages:', currentMessages.length);
    setLoading(true);
    setError(null);

    // We don't need to create a user message here anymore
    // since it's already added in the onSend function

    // Check if we're in the middle of a symptom assessment flow
    const assessmentActive = await AsyncStorage.getItem('symptomAssessmentActive');
    if (assessmentActive === 'true') {
      await handleSymptomAssessmentResponse(text);
      return;
    }

    // Check for special commands
    if (text.toLowerCase().includes('peak flow') || text.toLowerCase().includes('analyze my peak')) {
      await handlePeakFlowAnalysis();
      return;
    }

    // Check if user mentions not having a peak flow meter
    if (text.toLowerCase().includes("don't have a peak flow meter") ||
        text.toLowerCase().includes("do not have a peak flow meter") ||
        text.toLowerCase().includes("no peak flow meter") ||
        text.toLowerCase().includes("without a peak flow meter") ||
        text.toLowerCase().includes("can't get a peak flow meter") ||
        text.toLowerCase().includes("i don't have a peak flow meter")) {
      await handleNoPeakFlowMeter();
      return;
    }

    // If offline, use fallback responses
    if (!isOnline) {
      handleOfflineResponse(text);
      return;
    }

    // Otherwise, send to OpenRouter.ai API
    try {
      // Get user ID if available
      const userId = await AsyncStorage.getItem('userId');

      // Prepare user data for context
      const userData = {
        peakFlowData: peakFlowData || [],
        medicationData: medicationData || [],
        userId: userId || null,
        language: i18n.locale || 'en',
      };

      console.log('Sending message to OpenRouter API:', text);

      // Send message to OpenRouter API
      const response = await sendMessageToOpenRouter(text, currentMessages, userData);
      console.log('Received response from OpenRouter API:', response);

      if (isMounted.current) {
        // Add bot response to chat with a guaranteed unique ID for tracking typing animation
        const botMessage = createBotMessage(response);

        // Use the currentMessages parameter to ensure we have the latest state
        const updatedMessages = GiftedChat.append(currentMessages, [botMessage]);
        setMessages(updatedMessages);
        await saveMessages(updatedMessages); // Wait for save to complete
        setLoading(false);
      }
    } catch (error) {
      console.error('Error sending message to AI service:', error);

      if (isMounted.current) {
        setError(`Error: ${error.message || 'Failed to get response from AI service'}`);

        // Use offline fallback response
        handleOfflineResponse(text);
        setLoading(false);
      }
    }
  };

  // Handle offline responses with predefined answers
  const handleOfflineResponse = async (text, currentMessages = messages) => {
    let responseText = '';
    const lowerText = text.toLowerCase();

    if (lowerText.includes('hello') || lowerText.includes('hi') || lowerText.includes('hey')) {
      // Use the userName from component level
      responseText = `Hello ${userName}! How can I assist you today?`;
    }
    else if (lowerText.includes('inhaler') || lowerText.includes('puffer')) {
      responseText = "Inhalers are essential tools for asthma management. Here are some tips for proper inhaler use:\n\n• Always shake your inhaler before use\n• Breathe out fully before inhaling\n• Press the inhaler and breathe in slowly and deeply\n• Hold your breath for 10 seconds if possible\n• Wait at least 30 seconds before taking a second puff\n\nRemember to rinse your mouth after using corticosteroid inhalers to prevent thrush.";
    }
    else if (lowerText.includes('trigger') || lowerText.includes('cause')) {
      responseText = "Common asthma triggers include:\n\n• Allergens (pollen, dust mites, pet dander)\n• Air pollution and smoke\n• Cold air or sudden weather changes\n• Respiratory infections\n• Exercise\n• Stress and strong emotions\n• Certain medications\n\nIdentifying and avoiding your personal triggers is an important part of asthma management.";
    }
    else if (lowerText.includes('emergency') || lowerText.includes('attack') || lowerText.includes('severe')) {
      responseText = "During an asthma emergency:\n\n1. Stay calm and sit upright\n2. Take one puff of your reliever inhaler (usually blue) every 30-60 seconds, up to 10 puffs\n3. If symptoms don't improve after 10 puffs or you're worried at any point, call emergency services (911)\n4. Continue taking one puff every minute until help arrives if needed\n\nSEEK IMMEDIATE MEDICAL HELP if you experience severe shortness of breath, difficulty speaking, blue lips or fingernails, or if your reliever inhaler has no effect.";
    }
    else if (lowerText.includes('peak flow') || lowerText.includes('meter')) {
      responseText = "Peak flow meters measure how fast you can blow air out of your lungs. Regular monitoring can help you:\n\n• Detect asthma symptoms before they become severe\n• Identify triggers\n• Assess how well your treatment is working\n• Determine when to adjust medications or seek help\n\nFor best results, measure your peak flow at the same time each day, record the highest of three readings, and discuss patterns with your healthcare provider.";
    }
    else if (lowerText.includes("don't have a peak flow meter") ||
             lowerText.includes("do not have a peak flow meter") ||
             lowerText.includes("no peak flow meter") ||
             lowerText.includes("without a peak flow meter")) {
      responseText = "No problem if you don't have a peak flow meter! While peak flow meters are valuable tools, you can still effectively monitor your asthma through symptom tracking.\n\n" +
        "Pay attention to:\n" +
        "• How often you cough, wheeze, or feel short of breath\n" +
        "• Whether symptoms wake you at night\n" +
        "• How much your symptoms limit daily activities\n" +
        "• How often you need your rescue inhaler\n\n" +
        "Would you like me to guide you through a symptom-based assessment to help evaluate your current asthma control?";
    }
    else if (lowerText.includes('medication') || lowerText.includes('medicine') || lowerText.includes('drug')) {
      responseText = "Asthma medications generally fall into two categories:\n\n1. Quick-relief medications (rescue medications):\n   • Short-acting beta agonists like albuterol\n   • Used to quickly open airways during symptoms\n\n2. Long-term control medications:\n   • Inhaled corticosteroids\n   • Long-acting beta agonists\n   • Leukotriene modifiers\n   • Taken regularly to reduce inflammation and prevent symptoms\n\nAlways take medications as prescribed by your healthcare provider.";
    }
    else if (lowerText.includes('exercise') || lowerText.includes('physical') || lowerText.includes('activity')) {
      responseText = "Exercise is beneficial for people with asthma, but it can also trigger symptoms. Here are some tips:\n\n• Use your inhaler 15-30 minutes before exercise if recommended by your doctor\n• Warm up and cool down properly\n• Consider activities less likely to trigger asthma (swimming, walking, leisure biking)\n• Exercise in warm, humid environments when possible\n• Wear a mask or scarf in cold weather\n• Monitor air quality and avoid outdoor exercise on high pollution days\n\nRegular physical activity can actually improve your asthma control over time.";
    }
    else if (lowerText.includes('diet') || lowerText.includes('food') || lowerText.includes('eat')) {
      responseText = "While there's no specific diet for asthma, some nutritional approaches may help:\n\n• Eat plenty of fruits and vegetables (rich in antioxidants)\n• Consider foods with omega-3 fatty acids (fish, flaxseed)\n• Maintain a healthy weight\n• Stay hydrated\n• Be aware of food allergies that might trigger asthma\n• Some people find that sulfites (in wine, dried fruits, preserved foods) can trigger symptoms\n\nA balanced, healthy diet supports overall health and may help reduce inflammation.";
    }
    else if (lowerText.includes('stress') || lowerText.includes('anxiety') || lowerText.includes('emotion')) {
      responseText = "Stress and strong emotions can trigger or worsen asthma symptoms. Consider these stress management techniques:\n\n• Deep breathing exercises (when not having symptoms)\n• Progressive muscle relaxation\n• Mindfulness meditation\n• Regular physical activity\n• Adequate sleep\n• Counseling or therapy if needed\n\nIncorporating stress management into your daily routine may help improve your asthma control.";
    }
    else if (lowerText.includes('child') || lowerText.includes('kid') || lowerText.includes('baby')) {
      responseText = "Managing asthma in children requires special consideration:\n\n• Work with a pediatrician to develop an asthma action plan\n• Ensure caregivers and school staff know about your child's asthma\n• Help your child identify and express when they're having symptoms\n• Use age-appropriate devices (spacers, masks) for medication delivery\n• Create an asthma-friendly home environment\n• Encourage normal physical activity with proper precautions\n\nWith proper management, most children with asthma can lead fully active lives.";
    }
    else if (lowerText.includes('thank')) {
      responseText = "You're welcome! I'm happy to help with your asthma management questions. Feel free to ask if you need anything else!";
    }
    else {
      // Default response for unrecognized queries
      responseText = "I understand you're asking about asthma management. While I'm currently operating in offline mode with limited responses, I can provide information about:\n\n• Inhaler usage\n• Asthma triggers\n• Emergency management\n• Peak flow monitoring\n• Medications\n• Exercise with asthma\n• Diet and asthma\n• Stress management\n• Childhood asthma\n\nCould you please ask about one of these topics specifically?";
    }

    // Use the helper function to create the bot message with a guaranteed unique ID for typing animation
    const botMessage = createBotMessage(responseText);

    const updatedMessages = GiftedChat.append(currentMessages, [botMessage]);
    setMessages(updatedMessages);
    await saveMessages(updatedMessages); // Wait for save to complete
    setLoading(false);
  };

  // Handle responses during the symptom assessment flow
  const handleSymptomAssessmentResponse = async (text) => {
    try {
      const step = await AsyncStorage.getItem('symptomAssessmentStep');
      const lowerText = text.toLowerCase();

      // Step 1: Initial symptom assessment
      if (step === '1') {
        let severity = 'mild';
        let responseText = '';

        // Count symptoms based on user response
        const hasAllSymptoms = lowerText.includes('yes to all') || lowerText.includes('all of them');
        const hasNoSymptoms = lowerText.includes('no symptoms') || lowerText.includes('none of them');

        // Count individual symptoms
        const symptomCount = [
          'cough', 'wheezing', 'chest', 'breath', 'tight'
        ].filter(symptom => lowerText.includes(symptom)).length;

        // Determine severity based on symptom count
        if (hasAllSymptoms || symptomCount >= 3) {
          severity = 'severe';
          responseText = "Based on your symptoms, you appear to be experiencing significant asthma symptoms. " +
            "This could indicate poorly controlled asthma that needs attention.\n\n" +
            "Let me ask you a follow-up question: Are you experiencing any of these more serious symptoms?\n\n" +
            "• Difficulty speaking in full sentences due to breathlessness\n" +
            "• Waking up at night due to asthma symptoms\n" +
            "• Having to use your rescue inhaler more than usual";
        } else if (symptomCount >= 1 || !hasNoSymptoms) {
          severity = 'moderate';
          responseText = "Based on your symptoms, you appear to be experiencing some asthma symptoms. " +
            "This could indicate partially controlled asthma.\n\n" +
            "Let me ask you a follow-up question: How often are you experiencing these symptoms?\n\n" +
            "• Daily\n" +
            "• A few times a week\n" +
            "• Only occasionally";
        } else {
          severity = 'mild';
          responseText = "It's good to hear you're not experiencing symptoms right now. " +
            "This suggests your asthma may be well-controlled at the moment.\n\n" +
            "Let me ask you a follow-up question: Have you had any asthma symptoms in the past month? " +
            "For example, coughing, wheezing, or shortness of breath during exercise or at night?";
        }

        // Store the severity assessment
        await AsyncStorage.setItem('symptomAssessmentSeverity', severity);
        await AsyncStorage.setItem('symptomAssessmentStep', '2');

        // Send the response
        const botMessage = createBotMessage(responseText);

        // Add quick replies for easier interaction
        if (severity === 'severe') {
          botMessage.quickReplies = {
            type: 'radio',
            keepIt: true,
            values: [
              { title: 'Yes to all', value: 'yes to all serious symptoms' },
              { title: 'Some of them', value: 'some serious symptoms' },
              { title: 'None of them', value: 'no serious symptoms' },
            ],
          };
        } else if (severity === 'moderate') {
          botMessage.quickReplies = {
            type: 'radio',
            keepIt: true,
            values: [
              { title: 'Daily', value: 'daily symptoms' },
              { title: 'A few times a week', value: 'weekly symptoms' },
              { title: 'Only occasionally', value: 'occasional symptoms' },
            ],
          };
        } else {
          botMessage.quickReplies = {
            type: 'radio',
            keepIt: true,
            values: [
              { title: 'Yes', value: 'yes symptoms in past month' },
              { title: 'No', value: 'no symptoms in past month' },
            ],
          };
        }

        const updatedMessages = GiftedChat.append(messages, [botMessage]);
        setMessages(updatedMessages);
        saveMessages(updatedMessages);
      }
      // Step 2: Follow-up questions based on initial severity
      else if (step === '2') {
        const severity = await AsyncStorage.getItem('symptomAssessmentSeverity');
        let finalAssessment = '';
        let recommendations = '';

        // Process responses based on the previous severity assessment
        if (severity === 'severe') {
          if (lowerText.includes('yes to all') || lowerText.includes('all of them')) {
            finalAssessment = "Based on your responses, you appear to be experiencing SEVERE asthma symptoms. " +
              "This indicates your asthma may be poorly controlled and requires immediate attention.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Use your rescue inhaler as prescribed for immediate relief\n" +
              "• Contact your healthcare provider today if possible\n" +
              "• If symptoms worsen or don't improve with your rescue inhaler, seek emergency care\n" +
              "• Follow your asthma action plan for the red zone if you have one\n\n" +
              "Without a peak flow meter, it's important to monitor your symptoms closely. " +
              "Difficulty speaking, blue lips or fingernails, or severe breathlessness are emergency warning signs.";
          } else if (lowerText.includes('some') || lowerText.includes('yes') || !lowerText.includes('none')) {
            finalAssessment = "Based on your responses, you appear to be experiencing MODERATE TO SEVERE asthma symptoms. " +
              "This indicates your asthma may not be adequately controlled.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Use your rescue inhaler as prescribed for symptom relief\n" +
              "• Contact your healthcare provider within the next few days\n" +
              "• Review your controller medication use and ensure you're taking it as prescribed\n" +
              "• Identify and avoid triggers that may be worsening your symptoms\n\n" +
              "Without a peak flow meter, pay close attention to any worsening of symptoms and seek medical help if needed.";
          } else {
            finalAssessment = "Based on your responses, you appear to be experiencing MODERATE asthma symptoms. " +
              "While you have some significant symptoms, you're not currently showing signs of a severe asthma attack.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Continue using your controller medications as prescribed\n" +
              "• Use your rescue inhaler as needed for symptom relief\n" +
              "• Consider discussing your symptoms with your healthcare provider at your next visit\n" +
              "• Monitor for any worsening of symptoms\n\n" +
              "Without a peak flow meter, regular symptom monitoring is important. Consider keeping a symptom diary.";
          }
        } else if (severity === 'moderate') {
          if (lowerText.includes('daily')) {
            finalAssessment = "Based on your responses, you appear to be experiencing MODERATE asthma symptoms on a DAILY basis. " +
              "This suggests your asthma is not well-controlled.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Contact your healthcare provider to review your asthma management plan\n" +
              "• Ensure you're taking your controller medications correctly and consistently\n" +
              "• Identify and avoid triggers that may be worsening your symptoms\n" +
              "• Use your rescue inhaler as needed for immediate symptom relief\n\n" +
              "Without a peak flow meter, tracking your symptom frequency and severity can help your healthcare provider adjust your treatment.";
          } else if (lowerText.includes('week')) {
            finalAssessment = "Based on your responses, you appear to be experiencing MILD TO MODERATE asthma symptoms SEVERAL TIMES A WEEK. " +
              "This suggests your asthma is partially controlled.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Continue taking your controller medications as prescribed\n" +
              "• Consider discussing your symptoms with your healthcare provider\n" +
              "• Use your rescue inhaler as needed for symptom relief\n" +
              "• Identify and avoid triggers that may be causing these weekly symptoms\n\n" +
              "Without a peak flow meter, keeping track of when symptoms occur can help identify patterns and triggers.";
          } else {
            finalAssessment = "Based on your responses, you appear to be experiencing MILD asthma symptoms OCCASIONALLY. " +
              "This suggests your asthma is relatively well-controlled with occasional breakthrough symptoms.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Continue your current asthma management plan\n" +
              "• Use your rescue inhaler as needed for occasional symptoms\n" +
              "• Keep track of when symptoms occur to identify potential triggers\n" +
              "• Discuss any pattern of symptoms with your healthcare provider at your next visit\n\n" +
              "Without a peak flow meter, monitoring how often you need your rescue inhaler can be a good indicator of control.";
          }
        } else { // mild
          if (lowerText.includes('yes')) {
            finalAssessment = "Based on your responses, you appear to have MILD asthma with occasional symptoms in the past month. " +
              "This suggests your asthma is generally well-controlled with occasional breakthrough symptoms.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Continue your current asthma management plan\n" +
              "• Use your rescue inhaler as needed for occasional symptoms\n" +
              "• Keep track of when symptoms occur to identify potential triggers\n" +
              "• Discuss any pattern of symptoms with your healthcare provider at your next visit\n\n" +
              "Without a peak flow meter, you can still effectively monitor your asthma by tracking symptom frequency and severity.";
          } else {
            finalAssessment = "Based on your responses, you appear to have WELL-CONTROLLED asthma with NO symptoms in the past month. " +
              "This is excellent and suggests your current management plan is working well.";

            recommendations = "RECOMMENDATIONS:\n\n" +
              "• Continue your current asthma management plan\n" +
              "• Keep your rescue inhaler available even when feeling well\n" +
              "• Continue regular follow-ups with your healthcare provider\n" +
              "• Be prepared for seasonal changes or other triggers that might affect your asthma\n\n" +
              "Even without a peak flow meter, your symptom-free status indicates good control. Continue what you're doing!";
          }
        }

        // Reset the assessment state
        await AsyncStorage.removeItem('symptomAssessmentActive');
        await AsyncStorage.removeItem('symptomAssessmentStep');
        await AsyncStorage.removeItem('symptomAssessmentSeverity');

        // Send the final assessment and recommendations
        const assessmentMessage = createBotMessage(finalAssessment);
        const recommendationsMessage = createBotMessage(recommendations);

        // Add a final message about peak flow meters
        const finalMessage = createBotMessage(
          "This assessment is based solely on your reported symptoms and is not a substitute for medical advice or peak flow monitoring. " +
          "If possible, consider obtaining a peak flow meter for more objective monitoring of your asthma. " +
          "They are relatively inexpensive and provide valuable data for managing your condition.\n\n" +
          "Is there anything else you'd like to know about managing asthma without a peak flow meter?"
        );

        // Update the chat with all messages
        const updatedMessages = GiftedChat.append(messages, [assessmentMessage, recommendationsMessage, finalMessage]);
        setMessages(updatedMessages);
        saveMessages(updatedMessages);
      }
    } catch (error) {
      console.error('Error in symptom assessment flow:', error);
      setError('Failed to process your response. Please try again.');

      // Reset the assessment state in case of error
      await AsyncStorage.removeItem('symptomAssessmentActive');
      await AsyncStorage.removeItem('symptomAssessmentStep');
      await AsyncStorage.removeItem('symptomAssessmentSeverity');
    } finally {
      setLoading(false);
    }
  };

  // Handle the case when user doesn't have a peak flow meter
  const handleNoPeakFlowMeter = async () => {
    try {
      // Educational content about peak flow meters
      const educationalContent =
        "A peak flow meter is a small handheld device that measures how well air moves out of your lungs. " +
        "When you blow into it, it measures the fastest rate at which you can blow air out of your lungs. " +
        "This helps track your asthma control over time.\n\n" +
        "No problem if you don't have one! Let me guide you through a symptom-based assessment instead.";

      // Initial response with educational content
      const initialResponse = createBotMessage(
        educationalContent + "\n\nLet's start with a few questions about your symptoms. " +
        "Are you experiencing any of these symptoms right now?\n\n" +
        "• Coughing\n" +
        "• Wheezing\n" +
        "• Chest tightness\n" +
        "• Shortness of breath\n\n" +
        "Please answer with 'yes' or 'no' for each symptom, like: 'Yes to coughing and wheezing, no to the others'"
      );

      // Add quick replies for easier interaction
      initialResponse.quickReplies = {
        type: 'radio',
        keepIt: true,
        values: [
          {
            title: 'Yes to all symptoms',
            value: 'yes to all symptoms',
          },
          {
            title: 'No symptoms',
            value: 'no symptoms',
          },
          {
            title: 'Only some symptoms',
            value: 'only some symptoms',
          },
        ],
      };

      // Store the assessment state in AsyncStorage
      await AsyncStorage.setItem('symptomAssessmentActive', 'true');
      await AsyncStorage.setItem('symptomAssessmentStep', '1');

      // Add mock clinic information
      const clinicInfo =
        "\n\nIf you'd like to get a peak flow test, these clinics in Uganda offer the service:\n\n" +
        "• Mulago National Referral Hospital, Kampala\n" +
        "• Kiruddu General Hospital, Kampala\n" +
        "• Mbarara Regional Referral Hospital, Mbarara\n" +
        "• Gulu Regional Referral Hospital, Gulu\n" +
        "• Mbale Regional Referral Hospital, Mbale";

      // Add follow-up message with clinic information
      const clinicMessage = createBotMessage(clinicInfo);

      // Update the chat with both messages
      const updatedMessages = GiftedChat.append(messages, [initialResponse, clinicMessage]);
      setMessages(updatedMessages);
      saveMessages(updatedMessages);
    } catch (error) {
      console.error('Error in no peak flow meter handling:', error);
      setError('Failed to process your request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle peak flow analysis
  const handlePeakFlowAnalysis = async (currentMessages = messages) => {
    setLoading(true);
    setError(null);

    try {
      // Analyze peak flow data using our OpenRouter service
      const analysis = await analyzePeakFlow(peakFlowData);

      // Format the response
      const analysisText = `Based on your recent readings, your average peak flow is ${analysis.average} L/min.\n\n${analysis.status}\n\n${analysis.recommendation}\n\n${analysis.disclaimer}`;

      // Add bot response to chat
      const botMessage = createBotMessage(analysisText);

      const updatedMessages = GiftedChat.append(currentMessages, [botMessage]);
      setMessages(updatedMessages);
      await saveMessages(updatedMessages); // Wait for save to complete
    } catch (error) {
      console.error('Error analyzing peak flow:', error);
      setError('Error analyzing peak flow data. Please try again.');

      // Provide a fallback response
      const fallbackMessage = createBotMessage(
        "I'm having trouble analyzing your peak flow data. This could be due to insufficient data or a technical issue. " +
        "Please make sure you have recorded peak flow readings in the Symptom Tracker."
      );

      const updatedMessages = GiftedChat.append(currentMessages, [fallbackMessage]);
      setMessages(updatedMessages);
      await saveMessages(updatedMessages); // Wait for save to complete
    } finally {
      setLoading(false);
    }
  };

  // Import our custom TypingBubble component
  const TypingBubble = require('../components/TypingBubble').default;

  // Track which messages have completed typing animation
  const [typedMessages, setTypedMessages] = useState(new Set());

  // Customize chat bubble appearance with typing animation
  const renderBubble = (props) => {
    const { currentMessage } = props;
    const isUserMessage = currentMessage.user._id === 1;
    const messageId = currentMessage._id;
    const hasCompletedTyping = typedMessages.has(messageId);

    // Common styles for all bubbles
    const commonBubbleStyle = {
      borderRadius: 20, // Increased border radius for more rounded corners
      borderBottomLeftRadius: isUserMessage ? 20 : 5,
      borderBottomRightRadius: isUserMessage ? 5 : 20,
      marginBottom: 3,
    };

    // If it's a user message, render normal bubble
    if (isUserMessage) {
      return (
        <Bubble
          {...props}
          wrapperStyle={{
            right: {
              backgroundColor: COLORS.PRIMARY,
              ...commonBubbleStyle,
            },
            left: {
              backgroundColor: COLORS.LIGHT_BG,
              ...commonBubbleStyle,
            },
          }}
          textStyle={{
            right: {
              color: COLORS.WHITE,
              fontFamily: FONTS.REGULAR,
            },
            left: {
              color: COLORS.TEXT,
              fontFamily: FONTS.REGULAR,
            },
          }}
        />
      );
    }

    // For AI messages, use our custom component during typing or a styled bubble after typing
    if (hasCompletedTyping) {
      // For completed AI messages, use normal bubble with consistent styling
      return (
        <Bubble
          {...props}
          wrapperStyle={{
            left: {
              backgroundColor: COLORS.LIGHT_BG,
              ...commonBubbleStyle,
            },
          }}
          textStyle={{
            left: {
              color: COLORS.TEXT,
              fontFamily: FONTS.REGULAR,
            },
          }}
        />
      );
    } else {
      // For AI messages still typing, use our custom component
      return (
        <Bubble
          {...props}
          renderMessageText={() => (
            <View style={{
              padding: 10,
              paddingVertical: 8,
              backgroundColor: COLORS.LIGHT_BG,
              ...commonBubbleStyle,
            }}>
              <TypingBubble
                text={currentMessage.text}
                isUser={isUserMessage}
                typingSpeed={25}
                textStyle={{
                  color: COLORS.TEXT,
                  fontFamily: FONTS.REGULAR,
                }}
                onTypingComplete={() => {
                  // Mark this message as completed
                  setTypedMessages(prev => new Set([...prev, messageId]));
                }}
              />
            </View>
          )}
          wrapperStyle={{
            left: {
              backgroundColor: 'transparent',
            },
          }}
        />
      );
    }
  };

  // We're using a direct TouchableOpacity for the send button instead of a render function

  // Customize input toolbar - create a custom layout to separate input and send button
  const renderInputToolbar = (props) => {
    return (
      <View style={styles.customInputContainer}>
        <InputToolbar
          {...props}
          containerStyle={styles.inputToolbar}
          primaryStyle={styles.inputPrimary}
          renderSend={() => null} // Don't render the default send button
        />
        {/* Send button will be rendered separately */}
      </View>
    );
  };

  // Render loading indicator when waiting for bot response
  const renderFooter = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={COLORS.PRIMARY} />
          <Text style={styles.loadingText}>{i18n.t('chat.thinking')}</Text>
        </View>
      );
    }
    return null;
  };

  // No need to check for empty messages since we always send a welcome message on mount

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('chat.title') || 'AI Chat'}
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon={<Ionicons name="person-circle" size={24} color={COLORS.WHITE} />}
        onRightPress={() => navigation.navigate('Profile')}
      />

      {!isOnline && (
        <View style={styles.offlineWarning}>
          <Ionicons name="cloud-offline" size={16} color={COLORS.WHITE} />
          <Text style={styles.offlineText}>{i18n.t('chat.offlineMode') || 'Offline Mode'}</Text>
        </View>
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.chatContainer}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 20}
        enabled={true}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            <GiftedChat
              ref={chatRef}
              messages={messages}
              onSend={onSend}
              user={{
                _id: 1,
              }}
              renderBubble={renderBubble}
              renderInputToolbar={renderInputToolbar}
              renderFooter={renderFooter}
              placeholder={'Type a message...'}
              alwaysShowSend={false} // We're using our own send button
              scrollToBottom
              renderAvatar={null}
              scrollToBottomComponent={() => (
                <Ionicons name="chevron-down" size={20} color={COLORS.PRIMARY} />
              )}
              textInputProps={{
            style: {
              fontSize: 16,
              lineHeight: 20,
              color: COLORS.TEXT, // Ensure text is visible
              backgroundColor: COLORS.WHITE, // Ensure background is visible
              marginTop: 0,
              marginBottom: 0,
              marginRight: 60, // Increased space for the send button
              paddingTop: 10,
              paddingBottom: 10,
              paddingLeft: 12,
              paddingRight: 12,
              minHeight: 40, // Minimum height for single line
              maxHeight: 80, // Maximum height for 4 lines (20px line height * 4)
              width: '100%', // Use full width
              borderWidth: 0, // Remove any border that might interfere
              textAlignVertical: 'top', // Align text to top for multiline
            },
            returnKeyType: 'default', // Allow line breaks with Enter
            blurOnSubmit: false,
            multiline: true,
            numberOfLines: 1,
            maxLength: 1000, // Reasonable character limit
            placeholderTextColor: COLORS.TEXT_LIGHT, // Make placeholder visible
            onChangeText: (text) => {
              setInputText(text); // Update state
              inputTextRef.current = text; // Also store in ref for compatibility
            },
            value: inputText, // Use state value for controlled input
            autoGrow: true, // Enable auto-growing
            enableScrollToCaret: true, // Scroll to cursor position
          }}
          showUserAvatar={false}
          showAvatarForEveryMessage={false}
          renderUsernameOnMessage={false}
          minInputToolbarHeight={50}
          maxInputToolbarHeight={100}
          bottomOffset={Platform.OS === 'ios' ? 0 : 0}
          keyboardShouldPersistTaps="handled"
          inverted={true}
          listViewProps={{
            style: { marginBottom: 60 }, // Add margin for the input toolbar
            contentContainerStyle: { paddingBottom: 90 } // 75 + 15 (10% of original 150 padding)
          }}
            />
            {/* Custom send button */}
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: 15,
                bottom: 8,
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: COLORS.PRIMARY,
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 1000,
              }}
              onPress={() => {
                // Get the text from the state
                const text = inputText.trim();
                if (text.length > 0) {
                  // Use the existing onSend function to handle the message
                  onSend([{
                    _id: `user-${Date.now()}`,
                    text: text,
                    createdAt: new Date(),
                    user: { _id: 1 }
                  }]);

                  // Clear the input text
                  setInputText('');
                  inputTextRef.current = '';
                }
              }}
            >
              <Ionicons name="send" size={22} color={COLORS.WHITE} />
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  chatContainer: {
    flex: 1,
    paddingBottom: 72, // 60 + 12 (10% of original 120 padding)
  },
  customInputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10, // Increased padding
    paddingHorizontal: SPACING.medium,
    minHeight: 60,
    width: '100%',
  },
  inputToolbar: {
    flex: 1,
    backgroundColor: 'transparent',
    borderTopWidth: 0,
    paddingLeft: 0,
    paddingRight: 0,
    marginRight: 60, // Leave space for the send button
    paddingVertical: 0,
    marginBottom: 0,
    minHeight: 60,
  },

  inputPrimary: {
    alignItems: 'center',
    marginTop: 0,
    marginBottom: 0,
    paddingTop: 0,
    paddingBottom: 0,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_BG,
    backgroundColor: COLORS.WHITE,
    borderRadius: 24,
    paddingHorizontal: 15,
    marginRight: 0, // No need for margin as we're using container spacing
    flex: 1, // Take up available space
    width: '100%', // Use full width
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  absoluteSendContainer: {
    position: 'absolute',
    right: 15, // Position at the right with margin
    bottom: 8, // Align with the input bar
    zIndex: 999, // Ensure it's above other elements
    width: 48,
    height: 48,
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 0,
    marginBottom: 0,
    height: 48,
    width: 48,
    borderRadius: 24,
    backgroundColor: 'transparent', // Make container transparent
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  loadingContainer: {
    padding: SPACING.small,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: SPACING.medium,
    marginBottom: SPACING.small,
  },
  loadingText: {
    marginLeft: SPACING.small,
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT_LIGHT,
    fontStyle: 'italic',
  },
  offlineWarning: {
    backgroundColor: COLORS.WARNING,
    padding: SPACING.small,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  offlineText: {
    color: COLORS.WHITE,
    marginLeft: SPACING.small,
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  errorContainer: {
    backgroundColor: COLORS.DANGER + '20',
    padding: SPACING.small,
    margin: SPACING.small,
    borderRadius: BORDER_RADIUS.small,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.DANGER,
  },
  errorText: {
    color: COLORS.DANGER,
    fontSize: FONTS.SIZES.small,
  },
  // Removed unused styles
});

export default ChatScreen;
