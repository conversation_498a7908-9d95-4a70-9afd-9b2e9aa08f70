/**
 * Google Sign-In Test
 */

import React, { useEffect, useState } from 'react';
import { View, Text, Button, StyleSheet, Alert } from 'react-native';
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { GOOGLE_WEB_CLIENT_ID } from '@env';

export default function GoogleSignInTest() {
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Configure Google Sign-In
    configureGoogleSignIn();
    
    // Check if user is already signed in
    checkIsSignedIn();
  }, []);

  const configureGoogleSignIn = () => {
    try {
      console.log('Configuring Google Sign-In with client ID:', GOOGLE_WEB_CLIENT_ID);
      
      GoogleSignin.configure({
        webClientId: GOOGLE_WEB_CLIENT_ID,
        offlineAccess: true,
        forceCodeForRefreshToken: true,
      });
      
      console.log('Google Sign-In configured successfully');
    } catch (error) {
      console.error('Error configuring Google Sign-In:', error);
      setError('Configuration error: ' + error.message);
    }
  };

  const checkIsSignedIn = async () => {
    try {
      const isUserSignedIn = await GoogleSignin.isSignedIn();
      setIsSignedIn(isUserSignedIn);
      
      if (isUserSignedIn) {
        // Get user info if signed in
        const userInfo = await GoogleSignin.getCurrentUser();
        setUserInfo(userInfo);
      }
    } catch (error) {
      console.error('Error checking sign-in status:', error);
    }
  };

  const signIn = async () => {
    try {
      setError(null);
      
      // Check if Google Play Services are available
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      
      // Start the sign-in flow
      const userInfo = await GoogleSignin.signIn();
      console.log('User info:', userInfo);
      
      setUserInfo(userInfo);
      setIsSignedIn(true);
    } catch (error) {
      console.error('Sign-in error:', error);
      
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        setError('Sign in was cancelled');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        setError('Sign in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        setError('Play Services not available or outdated');
      } else {
        setError('Error: ' + (error.message || 'Unknown error'));
      }
    }
  };

  const signOut = async () => {
    try {
      await GoogleSignin.signOut();
      setUserInfo(null);
      setIsSignedIn(false);
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Sign-out error:', error);
      setError('Sign-out error: ' + error.message);
    }
  };

  const getCurrentUser = async () => {
    try {
      const currentUser = await GoogleSignin.getCurrentUser();
      if (currentUser) {
        Alert.alert('Current User', JSON.stringify(currentUser, null, 2));
      } else {
        Alert.alert('No User', 'No user is currently signed in.');
      }
    } catch (error) {
      console.error('Get current user error:', error);
      setError('Get current user error: ' + error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Google Sign-In Test</Text>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {isSignedIn ? 'Signed In' : 'Signed Out'}
        </Text>
      </View>
      
      {userInfo && (
        <View style={styles.userInfoContainer}>
          <Text style={styles.userInfoText}>User: {userInfo.user?.name}</Text>
          <Text style={styles.userInfoText}>Email: {userInfo.user?.email}</Text>
        </View>
      )}
      
      <View style={styles.buttonContainer}>
        {!isSignedIn ? (
          <Button title="Sign In with Google" onPress={signIn} />
        ) : (
          <Button title="Sign Out" onPress={signOut} />
        )}
      </View>
      
      <View style={styles.buttonContainer}>
        <Button 
          title="Get Current User" 
          onPress={getCurrentUser}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  statusContainer: {
    marginVertical: 10,
  },
  statusText: {
    fontSize: 16,
  },
  userInfoContainer: {
    marginVertical: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    width: '100%',
  },
  userInfoText: {
    fontSize: 14,
    marginBottom: 5,
  },
  buttonContainer: {
    marginVertical: 10,
    width: '100%',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
    width: '100%',
  },
  errorText: {
    color: '#d32f2f',
  },
});
