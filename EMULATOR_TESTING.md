# Testing with Firebase Emulator

This guide will help you test the Smart AsthmaCare app's chatbot functionality using the Firebase Emulator.

## Starting the Firebase Emulator

1. Open a terminal and navigate to your project directory:
   ```
   cd D:\Smart-AsthmaCare project\Smart-AsthmaCare
   ```

2. Start the Firebase Emulator:
   ```
   firebase emulators:start
   ```

3. You should see output similar to this:
   ```
   i  emulators: Starting emulators: functions
   ✔  hub: emulator hub started at http://localhost:4400
   i  ui: Emulator UI logging to ui-debug.log
   ✔  functions: Emulator started at http://localhost:5001
   i  functions: The following emulators are running: functions
   ✔  ui: Emulator UI started at http://localhost:4000
   ```

4. The Firebase Emulator UI will be available at http://localhost:4000
   - You can use this UI to monitor function calls and logs

## Testing on Different Devices

### Android Emulator
- The Android Emulator uses `********` to access your computer's localhost
- The app is already configured to use this address when running on Android

### iOS Simulator
- The iOS Simulator can access your computer's localhost directly using `localhost`
- The app is already configured to use this address when running on iOS

### Physical Devices
If you're testing on a physical device:

1. Find your computer's local IP address:
   - Windows: Open Command Prompt and type `ipconfig`
   - Mac: Open Terminal and type `ifconfig`
   - Look for your IPv4 address (e.g., 192.168.1.x)

2. Update the `firebase.js` file:
   ```javascript
   // Replace this line:
   const emulatorHost = Platform.OS === 'android' ? '********' : 'localhost';

   // With your actual IP address:
   const emulatorHost = '192.168.1.x'; // Replace with your actual IP
   ```

3. Make sure your device is on the same Wi-Fi network as your computer

## Troubleshooting

### Emulator Connection Issues
- Make sure the Firebase Emulator is running
- Check that you're using the correct host address for your device
- Verify that your firewall isn't blocking the connections

### Firebase Auth Initialization Issues
- If you see an error like "runtime not ready: Error: Component auth has not been registered yet", this is because Firebase Auth was previously being used but has now been disabled
- The Firebase Auth module has been commented out in the configuration since it's not needed for the current development phase
- The app now uses a simplified initialization process that only initializes the Firebase Functions service needed for the chatbot
- If you need to use Firebase Auth in the future, you can uncomment the relevant sections in `firebase.js`

### Function Execution Issues
- Check the Firebase Emulator UI logs at http://localhost:4000
- Look for errors in the function execution
- Verify that your Dialogflow key is correctly set up

### App Connection Issues
- The app has enhanced error handling for emulator connections
- Look for error messages in the chat interface
- Check the console logs in your development environment

## Testing Specific Features

### Testing the Chat Function
1. Start a conversation with a general question like "What is asthma?"
2. The app should connect to the emulator and return a response

### Testing Peak Flow Analysis
1. Add some peak flow data to your app
2. Ask "Analyze my peak flow" in the chat
3. The app should call the `analyzePeakFlow` function in the emulator

### Testing Medication Recommendations
1. Add some medication data to your app
2. Ask "Recommend medication" in the chat
3. The app should call the `recommendMedication` function in the emulator

## Logs and Debugging

- Function logs are available in the Firebase Emulator UI
- App logs are available in your development environment console
- The app will display user-friendly error messages when there are connection issues

## Switching to Production

When you're ready to deploy to production:
1. Upgrade your Firebase project to the Blaze plan
2. Deploy your functions using `firebase deploy --only functions`
3. Remove or comment out the emulator connection code in `firebase.js`
