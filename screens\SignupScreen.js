/**
 * Signup Screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Button from '../components/Button';
import Input from '../components/Input';
import CustomAlert from '../components/CustomAlert';
import GoogleSignInButton from '../components/GoogleSignInButton';
import { useAuth } from '../context/SupabaseAuthContext';
import i18n from '../i18n/i18n';

const SignupScreen = ({ navigation }) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  // Get auth functions from context
  const { signUp, signInWithGoogle } = useAuth();
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const validate = () => {
    const newErrors = {};

    if (!name) {
      newErrors.name = 'Name is required';
    }

    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!phone) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[0-9\s]{10,15}$/.test(phone.replace(/[\s-]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async () => {
    if (validate()) {
      setIsLoading(true);

      try {
        // Format phone number (remove spaces, ensure it has a + prefix if needed)
        let formattedPhone = phone.trim().replace(/\s+/g, '');
        if (formattedPhone && !formattedPhone.startsWith('+')) {
          // Add country code if missing (assuming Uganda +256 as default)
          if (formattedPhone.startsWith('0')) {
            formattedPhone = '+256' + formattedPhone.substring(1);
          } else {
            formattedPhone = '+' + formattedPhone;
          }
        }

        console.log('Signing up with phone number:', formattedPhone);

        // Call signUp function from auth context
        const result = await signUp(email, password, {
          name: name,
          phone: formattedPhone
        });

        if (result.success) {
          // Show success alert
          setAlertConfig({
            title: 'Account Created',
            message: 'Your account has been created successfully. A verification email has been sent to your email address.',
            type: 'success'
          });
          setAlertVisible(true);

          // Navigate to main screen after a short delay
          setTimeout(() => {
            navigation.replace('Main');
          }, 2000);
        } else {
          // Show error alert
          setAlertConfig({
            title: 'Signup Failed',
            message: result.error || 'Failed to create account',
            type: 'danger'
          });
          setAlertVisible(true);
        }
      } catch (error) {
        console.error('Signup error:', error);

        // Show error alert
        setAlertConfig({
          title: 'Signup Error',
          message: error.message || 'An unexpected error occurred',
          type: 'danger'
        });
        setAlertVisible(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);

    try {
      const result = await signInWithGoogle();

      if (result.success) {
        // Navigate to main screen on success
        navigation.replace('Main');
      } else {
        // Show error alert
        setAlertConfig({
          title: 'Google Sign-In Failed',
          message: result.error || 'Failed to sign in with Google',
          type: 'danger'
        });
        setAlertVisible(true);
      }
    } catch (error) {
      console.error('Google Sign-In error:', error);

      // Show error alert
      setAlertConfig({
        title: 'Google Sign-In Error',
        message: error.message || 'An unexpected error occurred',
        type: 'danger'
      });
      setAlertVisible(true);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.title}>Smart AsthmaCare</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>{i18n.t('auth.signup')}</Text>

          <Input
            label={i18n.t('auth.name')}
            placeholder="John Doe"
            value={name}
            onChangeText={setName}
            autoCapitalize="words"
            error={errors.name}
            icon={<Ionicons name="person-outline" size={20} color={COLORS.TEXT} />}
          />

          <Input
            label={i18n.t('auth.email')}
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
            icon={<Ionicons name="mail-outline" size={20} color={COLORS.TEXT} />}
          />

          <Input
            label={i18n.t('auth.phone')}
            placeholder="+256 XXX XXX XXX"
            value={phone}
            onChangeText={setPhone}
            keyboardType="phone-pad"
            error={errors.phone}
            icon={<Ionicons name="call-outline" size={20} color={COLORS.TEXT} />}
          />

          <Input
            label={i18n.t('auth.password')}
            placeholder="••••••••"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            error={errors.password}
            icon={<Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT} />}
          />

          <Input
            label={i18n.t('auth.confirmPassword')}
            placeholder="••••••••"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            error={errors.confirmPassword}
            icon={<Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT} />}
          />

          <Button
            title={i18n.t('auth.signup')}
            onPress={handleSignup}
            loading={isLoading}
            style={styles.signupButton}
          />

          <View style={styles.orContainer}>
            <View style={styles.divider} />
            <Text style={styles.orText}>OR</Text>
            <View style={styles.divider} />
          </View>

          <GoogleSignInButton
            onPress={handleGoogleSignIn}
            isLoading={isGoogleLoading}
            style={styles.googleButton}
          />

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>{i18n.t('auth.haveAccount')}</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>{i18n.t('auth.login')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Custom Alert */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.large,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SPACING.large,
    marginBottom: SPACING.medium,
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: SPACING.small,
  },
  title: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.large,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: SPACING.large,
  },
  formTitle: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.large,
    textAlign: 'center',
  },
  signupButton: {
    marginTop: SPACING.small,
    marginBottom: SPACING.medium,
  },
  orContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.LIGHT_GRAY,
  },
  orText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONTS.SIZES.small,
    marginHorizontal: SPACING.small,
  },
  googleButton: {
    marginBottom: SPACING.large,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: COLORS.TEXT,
    fontSize: FONTS.SIZES.medium,
    marginRight: SPACING.xs,
  },
  loginLink: {
    color: COLORS.PRIMARY,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
  },
});

export default SignupScreen;
