/**
 * Forgot Password Screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Button from '../components/Button';
import Input from '../components/Input';
import CustomAlert from '../components/CustomAlert';
import { useAuth } from '../context/SupabaseAuthContext';
import i18n from '../i18n/i18n';

const ForgotPasswordScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [resetSent, setResetSent] = useState(false);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  // Get auth functions from context
  const { resetPassword } = useAuth();

  // Add error boundary for auth context
  if (!resetPassword) {
    console.error('resetPassword function not available from auth context');
  }

  const validate = () => {
    const newErrors = {};

    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleResetPassword = async () => {
    if (validate()) {
      setIsLoading(true);

      try {
        // Check if resetPassword function is available
        if (!resetPassword) {
          throw new Error('Reset password functionality is not available');
        }

        // Call resetPassword function from auth context
        const result = await resetPassword(email);

        if (result.success) {
          setResetSent(true);

          // Show success alert with more detailed information
          setAlertConfig({
            title: i18n.t('auth.resetEmailSent'),
            message: `Password reset instructions have been sent to ${email}. Please check your email inbox and spam folder. If you don't receive the email within a few minutes, try again or contact support.`,
            type: 'success',
            onConfirm: () => {
              // In a real app, this would be handled by a deep link
              // For testing purposes, we'll navigate directly to the reset screen
              navigation.navigate('ResetPassword', { email, token: 'mock-token-123' });
            }
          });
          setAlertVisible(true);
        } else {
          // Show error alert with more helpful information
          let errorMessage = result.error || 'Failed to send reset email';

          // Provide more helpful error messages
          if (errorMessage.includes('User not found') || errorMessage.includes('Invalid email')) {
            errorMessage = `No account found with email ${email}. Please check the email address or create a new account.`;
          } else if (errorMessage.includes('rate limit') || errorMessage.includes('too many')) {
            errorMessage = 'Too many reset attempts. Please wait a few minutes before trying again.';
          } else if (errorMessage.includes('email not confirmed')) {
            errorMessage = 'Please verify your email address first before resetting your password.';
          }

          setAlertConfig({
            title: 'Reset Failed',
            message: errorMessage,
            type: 'danger'
          });
          setAlertVisible(true);
        }
      } catch (error) {
        // Show error alert
        setAlertConfig({
          title: 'Reset Error',
          message: error.message || 'An unexpected error occurred',
          type: 'danger'
        });
        setAlertVisible(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.TEXT} />
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.title}>Smart AsthmaCare</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>{i18n.t('auth.forgotPassword')}</Text>

          <Text style={styles.instructions}>
            {i18n.t('auth.forgotPasswordInstructions')}
          </Text>

          <Input
            label={i18n.t('auth.email')}
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
            icon={<Ionicons name="mail-outline" size={20} color={COLORS.TEXT} />}
            editable={!resetSent}
          />

          <Button
            title={resetSent ? i18n.t('auth.resendEmail') : i18n.t('auth.resetPassword')}
            onPress={handleResetPassword}
            loading={isLoading}
            style={styles.resetButton}
          />

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>{i18n.t('auth.rememberPassword')}</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>{i18n.t('auth.backToLogin')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Custom Alert */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
        onConfirm={alertConfig.onConfirm}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.large,
  },
  backButton: {
    position: 'absolute',
    top: SPACING.medium,
    left: SPACING.medium,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.WHITE,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SPACING.xxl * 2,
    marginBottom: SPACING.large,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: SPACING.small,
  },
  title: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.large,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  formTitle: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
    textAlign: 'center',
  },
  instructions: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.large,
    textAlign: 'center',
  },
  resetButton: {
    marginTop: SPACING.medium,
    marginBottom: SPACING.large,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  loginText: {
    color: COLORS.TEXT,
    fontSize: FONTS.SIZES.medium,
    marginRight: SPACING.xs,
  },
  loginLink: {
    color: COLORS.PRIMARY,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
  },
});

export default ForgotPasswordScreen;
