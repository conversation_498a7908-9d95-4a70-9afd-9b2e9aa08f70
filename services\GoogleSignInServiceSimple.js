/**
 * Simple Google Sign-In Service
 * A simplified implementation that works with minimal dependencies
 */

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GOOGLE_WEB_CLIENT_ID } from '@env';

// Constants
const STORAGE_KEY = '@GoogleSignIn:user';

// Mock user data for development
const MOCK_USER = {
  id: 'google-user-123456',
  email: '<EMAIL>',
  name: 'Google User',
  picture: 'https://ui-avatars.com/api/?name=Google+User&background=4285F4&color=fff',
  provider: 'google'
};

// Store user in AsyncStorage
const storeUser = async (user) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(user));
    return true;
  } catch (error) {
    console.error('Error storing Google user:', error);
    return false;
  }
};

// Get stored user from AsyncStorage
const getStoredUser = async () => {
  try {
    const userString = await AsyncStorage.getItem(STORAGE_KEY);
    if (userString) {
      return JSON.parse(userString);
    }
    return null;
  } catch (error) {
    console.error('Error getting stored Google user:', error);
    return null;
  }
};

// Clear stored user
const clearUser = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing Google user:', error);
    return false;
  }
};

// Main sign-in function
const signIn = async () => {
  try {
    console.log('Starting Google Sign-In process...');

    // For development, we'll use a mock implementation
    // In production, this would use the actual Google Sign-In flow

    // Store mock user
    await storeUser(MOCK_USER);

    // Generate a mock ID token
    const mockIdToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************.${Math.random().toString(36).substring(2)}`;

    return {
      success: true,
      user: MOCK_USER,
      idToken: mockIdToken,
      tokens: {
        id_token: mockIdToken,
        access_token: `mock-access-token-${Date.now()}`,
        refresh_token: `mock-refresh-token-${Date.now()}`,
        expires_at: Date.now() + 3600 * 1000
      }
    };
  } catch (error) {
    console.error('Google Sign-In error:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};

// Sign out function
const signOut = async () => {
  try {
    // Clear stored user
    await clearUser();
    return { success: true };
  } catch (error) {
    console.error('Google Sign-Out error:', error);
    return {
      success: false,
      error: error.message || 'Failed to sign out'
    };
  }
};

// Check if user is signed in
const isSignedIn = async () => {
  try {
    const user = await getStoredUser();
    return !!user;
  } catch (error) {
    console.error('Error checking sign-in status:', error);
    return false;
  }
};

// Get current user info
const getCurrentUser = async () => {
  try {
    return await getStoredUser();
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Export the service
export default {
  signIn,
  signOut,
  isSignedIn,
  getCurrentUser
};
