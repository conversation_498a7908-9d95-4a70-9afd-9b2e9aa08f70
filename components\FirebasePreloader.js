/**
 * Firebase Preloader Component
 * This component ensures Firebase is fully initialized before rendering the app
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { COLORS } from './theme';
import { auth, app } from '../config/firebase';

const FirebasePreloader = ({ children, onReady }) => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;

  useEffect(() => {
    const checkFirebase = async () => {
      try {
        // Check if Firebase app is initialized
        if (!app) {
          console.error('Firebase app is not initialized');
          setError('Firebase app is not initialized');
          setIsReady(true); // Continue anyway
          if (onReady) onReady();
          return;
        }

        // Check if auth is initialized
        if (!auth) {
          console.error('Firebase Auth is not initialized');
          setError('Firebase Auth is not initialized');
          setIsReady(true); // Continue anyway
          if (onReady) onReady();
          return;
        }

        // Try to access auth properties to verify it's working
        try {
          console.log('Checking Firebase Auth...');

          // Just accessing a property to see if it throws
          // We wrap this in a try-catch because it might throw if auth is not fully initialized
          const _ = auth.currentUser;

          console.log('Firebase Auth is ready');
          setIsReady(true);
          if (onReady) onReady();
        } catch (authError) {
          console.error('Firebase Auth access error:', authError);

          // Retry logic
          if (retryCount < MAX_RETRIES) {
            const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
            console.log(`Retrying Firebase initialization in ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
            setTimeout(() => {
              setRetryCount(prev => prev + 1);
            }, delay);
          } else {
            console.warn('Failed to initialize Firebase after multiple attempts, continuing anyway');
            setError('Failed to initialize Firebase after multiple attempts');
            // Continue anyway after max retries
            setIsReady(true);
            if (onReady) onReady();
          }
        }
      } catch (e) {
        console.error('Firebase check error:', e);
        setError(e.message);
        // Continue anyway after error
        setIsReady(true);
        if (onReady) onReady();
      }
    };

    if (!isReady) {
      checkFirebase();
    }
  }, [isReady, retryCount, onReady]);

  if (!isReady) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={COLORS.PRIMARY} />
        <Text style={styles.text}>Initializing app...</Text>
        {retryCount > 0 && (
          <Text style={styles.retryText}>
            Retry attempt {retryCount}/{MAX_RETRIES}
          </Text>
        )}
      </View>
    );
  }

  if (error) {
    console.warn('Firebase initialization warning:', error);
  }

  return children;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
  },
  text: {
    marginTop: 10,
    fontSize: 16,
    color: COLORS.TEXT,
  },
  retryText: {
    marginTop: 5,
    fontSize: 14,
    color: COLORS.WARNING,
  },
});

export default FirebasePreloader;
