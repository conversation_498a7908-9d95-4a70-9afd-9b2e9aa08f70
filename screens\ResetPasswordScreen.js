/**
 * Reset Password Screen
 */

import { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Button from '../components/Button';
import Input from '../components/Input';
import i18n from '../i18n/i18n';
import { showSuccess } from '../utils/alert';

const ResetPasswordScreen = ({ navigation, route }) => {
  const { email, token } = route.params || { email: '', token: '' };
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors = {};

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleResetPassword = () => {
    if (validate()) {
      setIsLoading(true);

      // Simulate API call
      setTimeout(() => {
        setIsLoading(false);

        // Show success message with styled alert
        showSuccess(
          i18n.t('auth.passwordResetSuccess'),
          i18n.t('auth.passwordResetSuccessMessage'),
          [
            {
              text: i18n.t('ok'),
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
      }, 1500);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.TEXT} />
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.title}>Smart AsthmaCare</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>{i18n.t('auth.createNewPassword')}</Text>

          <Text style={styles.instructions}>
            {i18n.t('auth.createNewPasswordInstructions')}
          </Text>

          <Input
            label={i18n.t('auth.newPassword')}
            placeholder="••••••••"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            error={errors.password}
            icon={<Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT} />}
          />

          <Input
            label={i18n.t('auth.confirmNewPassword')}
            placeholder="••••••••"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            error={errors.confirmPassword}
            icon={<Ionicons name="lock-closed-outline" size={20} color={COLORS.TEXT} />}
          />

          <Button
            title={i18n.t('auth.resetPassword')}
            onPress={handleResetPassword}
            loading={isLoading}
            style={styles.resetButton}
          />

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>{i18n.t('auth.rememberPassword')}</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>{i18n.t('auth.backToLogin')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.large,
  },
  backButton: {
    position: 'absolute',
    top: SPACING.medium,
    left: SPACING.medium,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.WHITE,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SPACING.xxl * 2,
    marginBottom: SPACING.large,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: SPACING.small,
  },
  title: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.large,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  formTitle: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
    textAlign: 'center',
  },
  instructions: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.large,
    textAlign: 'center',
  },
  resetButton: {
    marginTop: SPACING.medium,
    marginBottom: SPACING.large,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  loginText: {
    color: COLORS.TEXT,
    fontSize: FONTS.SIZES.medium,
    marginRight: SPACING.xs,
  },
  loginLink: {
    color: COLORS.PRIMARY,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
  },
});

export default ResetPasswordScreen;
