/**
 * Firebase Cloud Functions for Smart AsthmaCare app
 * Handles communication with Dialogflow ES
 */

const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });
const dialogflow = require('@google-cloud/dialogflow');
const { v4: uuidv4 } = require('uuid');
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp();

// Load the service account key JSON file
const serviceAccount = require('./dialogflow-key.json');

// Create a session client
const sessionClient = new dialogflow.SessionsClient({
  credentials: {
    client_email: serviceAccount.client_email,
    private_key: serviceAccount.private_key,
  },
  projectId: serviceAccount.project_id,
});

/**
 * HTTP endpoint for chat functionality
 * Receives user message and returns Dialogflow response
 */
exports.chat = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      // Check if request method is POST
      if (request.method !== 'POST') {
        return response.status(405).send({ error: 'Method Not Allowed' });
      }

      // Get message and language from request body
      const { message, language = 'en', userId, peakFlowData, medicationData } = request.body;

      if (!message) {
        return response.status(400).send({ error: 'Message is required' });
      }

      // Create a unique session ID for the user
      const sessionId = userId || request.headers.sessionid || uuidv4();

      // Create session path
      const sessionPath = sessionClient.projectAgentSessionPath(
        serviceAccount.project_id,
        sessionId
      );

      // Create the request for Dialogflow
      const dialogflowRequest = {
        session: sessionPath,
        queryInput: {
          text: {
            text: message,
            languageCode: language,
          },
        },
        queryParams: {
          payload: {
            fields: {
              userId: { stringValue: userId || 'anonymous' },
              peakFlowData: { stringValue: JSON.stringify(peakFlowData || []) },
              medicationData: { stringValue: JSON.stringify(medicationData || []) },
            }
          }
        }
      };

      // Send request to Dialogflow
      const [dialogflowResponse] = await sessionClient.detectIntent(dialogflowRequest);
      const result = dialogflowResponse.queryResult;

      // Check if we need to enhance the response with custom logic
      let enhancedResponse = result.fulfillmentText;

      // If the message is about peak flow and we have data, enhance the response
      if (
        (message.toLowerCase().includes('peak flow') ||
         result.intent?.displayName === 'PeakFlowAnalysis') &&
        peakFlowData &&
        Array.isArray(peakFlowData) &&
        peakFlowData.length > 0
      ) {
        const analysis = await analyzePeakFlowData(peakFlowData);
        enhancedResponse = `${result.fulfillmentText}\n\n${analysis.status}\n${analysis.recommendation}`;
      }

      // If the message is about medication and we have data, enhance the response
      if (
        (message.toLowerCase().includes('medication') ||
         result.intent?.displayName === 'MedicationRecommendation') &&
        medicationData &&
        Array.isArray(medicationData)
      ) {
        const recommendation = getMedicationRecommendation(medicationData);
        enhancedResponse = `${result.fulfillmentText}\n\n${recommendation}`;
      }

      // Return the response to the client
      return response.status(200).send({
        fulfillmentText: enhancedResponse,
        intent: result.intent ? result.intent.displayName : null,
        confidence: result.intentDetectionConfidence,
        sessionId: sessionId,
      });
    } catch (error) {
      console.error('Error processing message:', error);
      return response.status(500).send({
        error: 'An error occurred while processing your message',
        details: error.message,
      });
    }
  });
});

/**
 * Scheduled function to keep the Dialogflow agent warm
 * Runs every 30 minutes to prevent cold starts
 */
exports.keepWarm = functions.pubsub.schedule('every 30 minutes').onRun(async (context) => {
  try {
    const sessionId = uuidv4();
    const sessionPath = sessionClient.projectAgentSessionPath(
      serviceAccount.project_id,
      sessionId
    );

    const request = {
      session: sessionPath,
      queryInput: {
        text: {
          text: 'ping',
          languageCode: 'en',
        },
      },
    };

    await sessionClient.detectIntent(request);
    console.log('Kept Dialogflow agent warm');
    return null;
  } catch (error) {
    console.error('Error keeping agent warm:', error);
    return null;
  }
});

/**
 * Helper function to analyze peak flow data
 * @param {Array} peakFlowData - Array of peak flow readings
 * @returns {Object} Analysis results
 */
async function analyzePeakFlowData(peakFlowData) {
  try {
    // Filter valid readings
    const validReadings = peakFlowData.filter(item => item.peakFlow > 0);

    if (validReadings.length === 0) {
      return {
        average: 0,
        status: 'No valid peak flow readings found',
        recommendation: 'Please record your peak flow readings regularly',
        disclaimer: 'This analysis is for informational purposes only. Always consult with a healthcare professional.',
      };
    }

    // Calculate average and trend
    const sum = validReadings.reduce((total, item) => total + item.peakFlow, 0);
    const average = Math.round(sum / validReadings.length);

    // Sort readings by date
    const sortedReadings = [...validReadings].sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });

    // Calculate trend (if we have at least 3 readings)
    let trend = '';
    if (sortedReadings.length >= 3) {
      const recentReadings = sortedReadings.slice(-3);
      const firstReading = recentReadings[0].peakFlow;
      const lastReading = recentReadings[recentReadings.length - 1].peakFlow;

      if (lastReading > firstReading + 20) {
        trend = 'Your peak flow readings are showing an improving trend.';
      } else if (lastReading < firstReading - 20) {
        trend = 'Your peak flow readings are showing a declining trend. Monitor your symptoms closely.';
      } else {
        trend = 'Your peak flow readings are relatively stable.';
      }
    }

    // Determine status
    let status = '';
    if (average >= 350) {
      status = 'Your peak flow readings are in the green zone (good)';
    } else if (average >= 300) {
      status = 'Your peak flow readings are in the yellow zone (caution)';
    } else {
      status = 'Your peak flow readings are in the red zone (danger)';
    }

    // Determine recommendation
    let recommendation = '';
    if (average >= 350) {
      recommendation = 'Continue your current asthma management plan';
    } else if (average >= 300) {
      recommendation = 'Use your rescue inhaler as needed and monitor your symptoms closely';
    } else {
      recommendation = 'Use your rescue inhaler and contact your healthcare provider';
    }

    // Add trend information if available
    if (trend) {
      status = `${status}. ${trend}`;
    }

    return {
      average,
      status,
      recommendation,
      disclaimer: 'This analysis is for informational purposes only. Always consult with a healthcare professional.',
    };
  } catch (error) {
    console.error('Error in analyzePeakFlowData:', error);
    return {
      status: 'Unable to analyze peak flow data',
      recommendation: 'Please consult with your healthcare provider',
      disclaimer: 'This analysis is for informational purposes only. Always consult with a healthcare professional.',
    };
  }
}

/**
 * Helper function to get medication recommendations
 * @param {Array} medicationData - Array of medication data
 * @returns {String} Recommendation text
 */
function getMedicationRecommendation(medicationData) {
  try {
    if (!medicationData || medicationData.length === 0) {
      return 'No medication data available. Please add your medications to receive personalized recommendations.';
    }

    // Count medication types
    const hasRescueInhaler = medicationData.some(med =>
      med.type?.toLowerCase().includes('rescue') ||
      med.name?.toLowerCase().includes('albuterol') ||
      med.name?.toLowerCase().includes('ventolin')
    );

    const hasController = medicationData.some(med =>
      med.type?.toLowerCase().includes('controller') ||
      med.name?.toLowerCase().includes('fluticasone') ||
      med.name?.toLowerCase().includes('advair') ||
      med.name?.toLowerCase().includes('symbicort')
    );

    // Generate recommendation based on medication profile
    let recommendation = '';

    if (!hasRescueInhaler) {
      recommendation += 'It appears you don\'t have a rescue inhaler in your medication list. A rescue inhaler is essential for managing sudden asthma symptoms. Please consult your healthcare provider about getting one.\n\n';
    }

    if (!hasController) {
      recommendation += 'You don\'t seem to have a controller medication listed. Controller medications help prevent asthma symptoms when taken regularly. Consider discussing this with your healthcare provider.\n\n';
    }

    if (hasRescueInhaler && hasController) {
      recommendation += 'You have both rescue and controller medications, which is the recommended approach for most people with asthma. Remember to:\n';
      recommendation += '- Take your controller medication daily as prescribed\n';
      recommendation += '- Keep your rescue inhaler with you at all times\n';
      recommendation += '- Use your rescue inhaler when you experience symptoms\n';
      recommendation += '- Track how often you use your rescue inhaler - frequent use may indicate your asthma is not well controlled\n\n';
    }

    recommendation += 'Disclaimer: This is general information only. Always follow your healthcare provider\'s specific instructions for your medications.';

    return recommendation;
  } catch (error) {
    console.error('Error in getMedicationRecommendation:', error);
    return 'Unable to provide medication recommendations. Please consult with your healthcare provider.';
  }
}

/**
 * HTTP endpoint for analyzing peak flow readings
 * This would normally connect to a more sophisticated ML model
 * but for now it uses simple rules
 */
exports.analyzePeakFlow = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      // Check if request method is POST
      if (request.method !== 'POST') {
        return response.status(405).send({ error: 'Method Not Allowed' });
      }

      // Get peak flow data from request body
      const { peakFlowData, language = 'en' } = request.body;

      if (!peakFlowData || !Array.isArray(peakFlowData)) {
        return response.status(400).send({ error: 'Valid peak flow data is required' });
      }

      const analysis = await analyzePeakFlowData(peakFlowData);

      // Return the analysis
      return response.status(200).send(analysis);
    } catch (error) {
      console.error('Error analyzing peak flow:', error);
      return response.status(500).send({
        error: 'An error occurred while analyzing your peak flow data',
        details: error.message,
      });
    }
  });
});

/**
 * HTTP endpoint for medication recommendations
 */
exports.recommendMedication = functions.https.onRequest((request, response) => {
  cors(request, response, async () => {
    try {
      // Check if request method is POST
      if (request.method !== 'POST') {
        return response.status(405).send({ error: 'Method Not Allowed' });
      }

      // Get medication data from request body
      const { medicationData, peakFlowData, language = 'en' } = request.body;

      // Generate recommendation
      const recommendation = getMedicationRecommendation(medicationData || []);

      // If we have peak flow data, include that in the analysis
      let peakFlowAnalysis = null;
      if (peakFlowData && Array.isArray(peakFlowData) && peakFlowData.length > 0) {
        peakFlowAnalysis = await analyzePeakFlowData(peakFlowData);
      }

      // Return the recommendation
      return response.status(200).send({
        recommendation,
        peakFlowAnalysis,
        disclaimer: 'This recommendation is for informational purposes only. Always consult with a healthcare professional.',
      });
    } catch (error) {
      console.error('Error generating medication recommendation:', error);
      return response.status(500).send({
        error: 'An error occurred while generating your medication recommendation',
        details: error.message,
      });
    }
  });
});