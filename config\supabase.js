/**
 * Simplified Supabase client for Smart AsthmaCare app
 * This implementation avoids using WebSockets which cause issues in React Native
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-url-polyfill/auto';

// Supabase configuration
// Get values from global ENV object if available, otherwise use hardcoded values
let supabaseUrl, supabaseAnonKey;

// Check if global ENV object exists (created in App.js)
if (global.ENV && global.ENV.SUPABASE_URL && global.ENV.SUPABASE_ANON_KEY) {
  supabaseUrl = global.ENV.SUPABASE_URL;
  supabaseAnonKey = global.ENV.SUPABASE_ANON_KEY;
  console.log('Using Supabase credentials from global ENV object');
} else {
  // Fallback to hardcoded values
  supabaseUrl = 'https://zwinrrbdlecsbuogvzky.supabase.co';
  supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3aW5ycmJkbGVjc2J1b2d2emt5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMjYzNTQsImV4cCI6MjA2MjkwMjM1NH0.YdtUNNnyWpzZvzcLLfrEV6Y-q6Tpouakm0E6klALhVM';
  console.log('Could not load environment variables, using hardcoded values');
}

// Add debug logs
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key length:', supabaseAnonKey ? supabaseAnonKey.length : 0);

// Math.random.seed polyfill is now in shim.js
console.log('Using Math.random.seed from shim.js');

// Create a simplified Supabase client
const supabase = {
  auth: {
    // Sign up with email and password
    signUp: async (params) => {
      try {
        console.log('Signing up with params:', JSON.stringify({
          email: params.email,
          options: params.options
        }));

        const response = await fetch(`${supabaseUrl}/auth/v1/signup`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Authorization': `Bearer ${supabaseAnonKey}`
          },
          body: JSON.stringify({
            email: params.email,
            password: params.password,
            data: params.options?.data
          })
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error('Signup error from API:', responseData);
          return { error: responseData, data: null };
        }

        // Format the response to match Supabase JS client
        const data = {
          user: responseData.user,
          session: {
            access_token: responseData.access_token,
            refresh_token: responseData.refresh_token,
            user: responseData.user
          }
        };

        // Store user in AsyncStorage
        if (data.user) {
          await AsyncStorage.setItem('supabase.auth.token', JSON.stringify({
            access_token: responseData.access_token,
            refresh_token: responseData.refresh_token,
            user: responseData.user
          }));
        }

        console.log('Signup successful, user:', data.user?.email);
        return { data, error: null };
      } catch (error) {
        console.error('Sign up error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Sign in with email and password
    signInWithPassword: async (params) => {
      try {
        console.log('Signing in with email:', params.email);

        const response = await fetch(`${supabaseUrl}/auth/v1/token?grant_type=password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Authorization': `Bearer ${supabaseAnonKey}`
          },
          body: JSON.stringify({ email: params.email, password: params.password })
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error('Login error from API:', responseData);
          return { error: responseData, data: null };
        }

        // Format the response to match Supabase JS client
        const data = {
          user: responseData.user,
          session: {
            access_token: responseData.access_token,
            refresh_token: responseData.refresh_token,
            user: responseData.user
          }
        };

        // Store user in AsyncStorage
        if (data.user) {
          await AsyncStorage.setItem('supabase.auth.token', JSON.stringify({
            access_token: responseData.access_token,
            refresh_token: responseData.refresh_token,
            user: responseData.user
          }));
        }

        console.log('Login successful, user:', data.user?.email);
        return { data, error: null };
      } catch (error) {
        console.error('Sign in error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Sign in with Google
    signInWithIdToken: async (params) => {
      try {
        console.log('Google Sign-In attempt with params:', JSON.stringify({
          provider: params.provider,
          token: params.token ? 'token-provided' : 'no-token'
        }));

        // Extract provider and token if available
        const provider = params.provider || 'google';
        const token = params.token || params.id_token || 'mock-token';

        // For testing purposes, create a mock user
        // In a real implementation, we would validate the token with Supabase
        const mockUser = {
          id: `${provider}-${Date.now()}`,
          email: `user-${Date.now()}@example.com`,
          user_metadata: {
            name: `${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
            avatar_url: `https://ui-avatars.com/api/?name=${provider}+User&background=4285F4&color=fff`
          },
          app_metadata: {
            provider: provider
          }
        };

        console.log('Created mock user:', mockUser.email);

        // Store mock user in AsyncStorage
        await AsyncStorage.setItem('supabase.auth.token', JSON.stringify({
          access_token: `mock-token-${Date.now()}`,
          refresh_token: `refresh-token-${Date.now()}`,
          user: mockUser
        }));

        // Format the response to match Supabase JS client
        const data = {
          user: mockUser,
          session: {
            access_token: `mock-token-${Date.now()}`,
            refresh_token: `refresh-token-${Date.now()}`,
            user: mockUser
          }
        };

        return { data, error: null };
      } catch (error) {
        console.error('Google sign in error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Reset password
    resetPasswordForEmail: async (email, options = {}) => {
      try {
        console.log('Resetting password for email:', email);

        // Call the real Supabase API for password reset
        const response = await fetch(`${supabaseUrl}/auth/v1/recover`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Authorization': `Bearer ${supabaseAnonKey}`
          },
          body: JSON.stringify({
            email: email,
            gotrue_meta_security: {},
            ...(options.redirectTo && { redirect_to: options.redirectTo })
          })
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error('Reset password error from API:', responseData);
          return { error: responseData, data: null };
        }

        console.log('Password reset email sent successfully via Supabase API');
        return { data: responseData, error: null };
      } catch (error) {
        console.error('Reset password error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Update user
    updateUser: async (params) => {
      try {
        console.log('Updating user with params:', JSON.stringify({
          email: params.email ? 'email-provided' : 'no-email',
          data: params.data ? 'data-provided' : 'no-data'
        }));

        // Get current user
        const tokenString = await AsyncStorage.getItem('supabase.auth.token');
        if (!tokenString) {
          return { error: { message: 'No user is signed in' }, data: null };
        }

        const token = JSON.parse(tokenString);
        const user = token.user;

        // Update user data
        const updatedUser = { ...user };

        if (params.email) {
          updatedUser.email = params.email;
        }

        if (params.data) {
          updatedUser.user_metadata = {
            ...updatedUser.user_metadata,
            ...params.data
          };
        }

        // Store updated user in AsyncStorage
        await AsyncStorage.setItem('supabase.auth.token', JSON.stringify({
          ...token,
          user: updatedUser
        }));

        return { data: { user: updatedUser }, error: null };
      } catch (error) {
        console.error('Update user error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Admin functions
    admin: {
      // Delete user
      deleteUser: async (userId) => {
        try {
          console.log('Would delete user with ID:', userId);

          // In a real implementation, this would call the Supabase Admin API
          // For now, just simulate a successful response

          // Clear user from AsyncStorage
          await AsyncStorage.removeItem('supabase.auth.token');

          return { data: {}, error: null };
        } catch (error) {
          console.error('Delete user error:', error);
          return { error: { message: error.message }, data: null };
        }
      }
    },

    // Sign out
    signOut: async () => {
      try {
        console.log('Signing out user');

        // Clear user from AsyncStorage
        await AsyncStorage.removeItem('supabase.auth.token');
        return { error: null };
      } catch (error) {
        console.error('Sign out error:', error);
        return { error: { message: error.message } };
      }
    },

    // Get current session
    getSession: async () => {
      try {
        const tokenString = await AsyncStorage.getItem('supabase.auth.token');
        if (!tokenString) {
          return { data: { session: null }, error: null };
        }

        const token = JSON.parse(tokenString);
        return {
          data: {
            session: {
              access_token: token.access_token,
              refresh_token: token.refresh_token,
              user: token.user
            }
          },
          error: null
        };
      } catch (error) {
        console.error('Get session error:', error);
        return { data: { session: null }, error: { message: error.message } };
      }
    },

    // Get current user
    getUser: async () => {
      try {
        const tokenString = await AsyncStorage.getItem('supabase.auth.token');
        if (!tokenString) {
          return { data: { user: null }, error: null };
        }

        const token = JSON.parse(tokenString);
        return { data: { user: token.user }, error: null };
      } catch (error) {
        console.error('Get user error:', error);
        return { data: { user: null }, error: { message: error.message } };
      }
    },

    // Auth state change listener
    onAuthStateChange: (eventCallback) => {
      // This is a simplified version that doesn't actually listen for changes
      // In a real implementation, this would set up a listener

      // Call the callback once with the current session
      setTimeout(async () => {
        try {
          const tokenString = await AsyncStorage.getItem('supabase.auth.token');
          if (tokenString) {
            const token = JSON.parse(tokenString);
            // Simulate a SIGNED_IN event
            eventCallback('SIGNED_IN', {
              session: {
                access_token: token.access_token,
                refresh_token: token.refresh_token,
                user: token.user
              }
            });
          } else {
            // Simulate a SIGNED_OUT event
            eventCallback('SIGNED_OUT', {
              session: null
            });
          }
        } catch (error) {
          console.error('Error in auth state change listener:', error);
        }
      }, 0);

      // Return a subscription object
      return {
        subscription: {
          unsubscribe: () => {
            console.log('Auth state change listener unsubscribed');
          }
        }
      };
    }
  },

  // Database operations
  from: (table) => ({
    select: (columns = '*') => ({
      eq: (column, value) => ({
        single: async () => {
          try {
            const response = await fetch(`${supabaseUrl}/rest/v1/${table}?select=${columns}&${column}=eq.${value}&limit=1`, {
              headers: {
                'apikey': supabaseAnonKey,
                'Authorization': `Bearer ${supabaseAnonKey}`
              }
            });

            const data = await response.json();

            if (!response.ok) {
              return { error: data, data: null };
            }

            return { data: data[0] || null, error: null };
          } catch (error) {
            console.error(`Error selecting from ${table}:`, error);
            return { data: null, error: { message: error.message } };
          }
        }
      })
    }),

    insert: async (records) => {
      try {
        const response = await fetch(`${supabaseUrl}/rest/v1/${table}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Authorization': `Bearer ${supabaseAnonKey}`,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(records)
        });

        const data = await response.json();

        if (!response.ok) {
          return { error: data, data: null };
        }

        return { data, error: null };
      } catch (error) {
        console.error(`Error inserting into ${table}:`, error);
        return { error: { message: error.message }, data: null };
      }
    },

    update: async (updates) => {
      try {
        // This is a simplified version that doesn't actually update anything
        // In a real implementation, this would make a PATCH request to the Supabase API
        console.log(`Would update ${table} with:`, updates);
        return { error: null };
      } catch (error) {
        console.error(`Error updating ${table}:`, error);
        return { error: { message: error.message } };
      }
    },

    delete: async () => {
      try {
        // This is a simplified version that doesn't actually delete anything
        // In a real implementation, this would make a DELETE request to the Supabase API
        console.log(`Would delete from ${table}`);
        return { error: null };
      } catch (error) {
        console.error(`Error deleting from ${table}:`, error);
        return { error: { message: error.message } };
      }
    }
  })
};

// Add a method to get the current auth token
supabase.getAuthToken = async () => {
  try {
    const tokenString = await AsyncStorage.getItem('supabase.auth.token');
    if (!tokenString) return null;

    const token = JSON.parse(tokenString);
    return token.access_token;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Add a method to get auth headers
supabase.getAuthHeaders = async () => {
  const token = await supabase.getAuthToken();
  return {
    'Content-Type': 'application/json',
    'apikey': supabaseAnonKey,
    'Authorization': token ? `Bearer ${token}` : `Bearer ${supabaseAnonKey}`
  };
};

// Enhance the from method to use auth headers
const originalFrom = supabase.from;
supabase.from = (table) => {
  const originalMethods = originalFrom(table);

  return {
    ...originalMethods,
    select: (columns = '*') => {
      const originalSelect = originalMethods.select(columns);

      return {
        ...originalSelect,
        eq: (column, value) => {
          const originalEq = originalSelect.eq(column, value);

          return {
            ...originalEq,
            single: async () => {
              try {
                const headers = await supabase.getAuthHeaders();
                const response = await fetch(`${supabaseUrl}/rest/v1/${table}?select=${columns}&${column}=eq.${value}&limit=1`, {
                  headers
                });

                const data = await response.json();
                if (!response.ok) {
                  console.error(`Error selecting from ${table}:`, data);
                  return { error: data, data: null };
                }

                return { data: data[0] || null, error: null };
              } catch (error) {
                console.error(`Error selecting from ${table}:`, error);
                return { data: null, error: { message: error.message } };
              }
            }
          };
        }
      };
    },
    insert: async (records) => {
      try {
        const headers = await supabase.getAuthHeaders();
        const response = await fetch(`${supabaseUrl}/rest/v1/${table}`, {
          method: 'POST',
          headers: {
            ...headers,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(records)
        });

        const data = await response.json();
        if (!response.ok) {
          console.error(`Error inserting into ${table}:`, data);
          return { error: data, data: null };
        }

        return { data, error: null };
      } catch (error) {
        console.error(`Error inserting into ${table}:`, error);
        return { error: { message: error.message }, data: null };
      }
    },
    update: async (updates) => {
      try {
        const headers = await supabase.getAuthHeaders();
        const response = await fetch(`${supabaseUrl}/rest/v1/${table}`, {
          method: 'PATCH',
          headers: {
            ...headers,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(updates)
        });

        const data = await response.json();
        if (!response.ok) {
          console.error(`Error updating ${table}:`, data);
          return { error: data, data: null };
        }

        return { data, error: null };
      } catch (error) {
        console.error(`Error updating ${table}:`, error);
        return { error: { message: error.message }, data: null };
      }
    },
    delete: () => ({
      eq: (column, value) => async () => {
        try {
          console.log(`Deleting from ${table} where ${column} = ${value}`);

          // For UUID columns, we need to ensure the value is properly formatted
          let formattedValue = value;
          if (column === 'id' && typeof value === 'string' && value.length === 36) {
            // This is likely a UUID, so we'll format it properly
            formattedValue = value;
          }

          const headers = await supabase.getAuthHeaders();

          // Add more detailed logging
          console.log(`DELETE request URL: ${supabaseUrl}/rest/v1/${table}?${column}=eq.${formattedValue}`);
          console.log('Headers:', headers);

          const response = await fetch(`${supabaseUrl}/rest/v1/${table}?${column}=eq.${formattedValue}`, {
            method: 'DELETE',
            headers: {
              ...headers,
              'Prefer': 'return=representation'
            }
          });

          // Log the response status
          console.log(`DELETE response status: ${response.status} ${response.statusText}`);

          if (!response.ok) {
            let errorData;
            try {
              errorData = await response.json();
            } catch (e) {
              errorData = { message: 'Failed to parse error response' };
            }
            console.error(`Error deleting from ${table}:`, errorData);
            return { error: errorData, data: null };
          }

          // Try to parse the response
          let data = null;
          try {
            const text = await response.text();
            console.log(`DELETE response text: ${text}`);
            if (text && text.trim()) {
              data = JSON.parse(text);
            }
          } catch (parseError) {
            console.log('No JSON response from delete operation or failed to parse');
          }

          console.log(`Successfully deleted from ${table} where ${column} = ${formattedValue}`);
          return { data, error: null };
        } catch (error) {
          console.error(`Exception when deleting from ${table}:`, error);
          return { error: { message: error.message }, data: null };
        }
      }
    })
  };
};

// Export the enhanced Supabase client
module.exports = supabase;
export default supabase;
