/**
 * Authentication Context
 * Provides authentication state and methods throughout the app
 */

import React, { createContext, useState, useEffect, useContext } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  updateProfile,
  updateEmail,
  updatePassword,
  deleteUser,
  sendEmailVerification
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Add debug logs to help troubleshoot
console.log('AuthContext imported, auth available:', !!auth);
console.log('Firebase auth object type:', typeof auth);

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Log auth status on mount
  useEffect(() => {
    console.log('AuthProvider mounted, auth available:', !!auth);
    if (!auth) {
      console.warn('Auth is not available, running in fallback mode');
      setError('Authentication service is not available');
      setLoading(false);
    }
  }, []);

  // Sign up function
  const signup = async (email, password, name, phone) => {
    try {
      setError(null);
      // Create user with email and password
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update profile with display name
      await updateProfile(user, {
        displayName: name
      });

      // Create user profile document in Firestore
      const userProfile = {
        uid: user.uid,
        email: email,
        name: name,
        phone: phone,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Save user profile to Firestore
      await setDoc(doc(db, 'users', user.uid), userProfile);

      // Send email verification
      await sendEmailVerification(user);

      return { success: true, user };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Login function
  const login = async (email, password) => {
    try {
      setError(null);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setError(null);
      await signOut(auth);
      // Clear any user data from AsyncStorage
      await AsyncStorage.removeItem('userProfile');
      return { success: true };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Reset password function
  const resetPassword = async (email) => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
      return { success: true };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Update user profile
  const updateUserProfile = async (data) => {
    try {
      setError(null);
      const user = auth.currentUser;

      if (!user) {
        throw new Error('No user is signed in');
      }

      // Update display name if provided
      if (data.name) {
        await updateProfile(user, {
          displayName: data.name
        });
      }

      // Update email if provided
      if (data.email && data.email !== user.email) {
        await updateEmail(user, data.email);
      }

      // Update password if provided
      if (data.password) {
        await updatePassword(user, data.password);
      }

      // Update user profile in Firestore
      const userRef = doc(db, 'users', user.uid);
      const updatedData = {
        ...data,
        updatedAt: new Date().toISOString()
      };

      // Remove password from data to be stored
      if (updatedData.password) {
        delete updatedData.password;
      }

      await updateDoc(userRef, updatedData);

      // Fetch updated profile
      await fetchUserProfile();

      return { success: true };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Delete user account
  const deleteUserAccount = async () => {
    try {
      setError(null);
      const user = auth.currentUser;

      if (!user) {
        throw new Error('No user is signed in');
      }

      // Delete user data from Firestore
      await deleteDoc(doc(db, 'users', user.uid));

      // Delete user authentication
      await deleteUser(user);

      // Clear any user data from AsyncStorage
      await AsyncStorage.removeItem('userProfile');

      return { success: true };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    }
  };

  // Fetch user profile from Firestore
  const fetchUserProfile = async () => {
    try {
      const user = auth.currentUser;

      if (!user) {
        setUserProfile(null);
        return null;
      }

      const userRef = doc(db, 'users', user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        setUserProfile(userData);

        // Cache user profile in AsyncStorage
        await AsyncStorage.setItem('userProfile', JSON.stringify(userData));

        return userData;
      } else {
        // If user document doesn't exist, create it with basic info
        const newUserProfile = {
          uid: user.uid,
          email: user.email,
          name: user.displayName || '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await setDoc(userRef, newUserProfile);
        setUserProfile(newUserProfile);

        // Cache user profile in AsyncStorage
        await AsyncStorage.setItem('userProfile', JSON.stringify(newUserProfile));

        return newUserProfile;
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  };

  // Effect to handle auth state changes
  useEffect(() => {
    let unsubscribe = () => {};
    let retryCount = 0;
    const MAX_RETRIES = 3;

    const setupAuthListener = () => {
      try {
        // Check if auth is initialized
        if (auth) {
          console.log('Setting up auth state listener');

          try {
            unsubscribe = onAuthStateChanged(auth, async (user) => {
              console.log('Auth state changed:', user ? 'User logged in' : 'No user');
              setCurrentUser(user);

              if (user) {
                // Try to get cached profile first for faster loading
                try {
                  const cachedProfile = await AsyncStorage.getItem('userProfile');
                  if (cachedProfile) {
                    setUserProfile(JSON.parse(cachedProfile));
                  }
                } catch (e) {
                  console.error('Error reading cached profile:', e);
                }

                // Fetch fresh profile data
                await fetchUserProfile();
              } else {
                setUserProfile(null);
              }

              setLoading(false);
            }, (error) => {
              console.error('Auth state change error:', error);
              setError(error.message);
              setLoading(false);
            });

            console.log('Auth state listener set up successfully');
          } catch (listenerError) {
            console.error('Error setting up auth state listener:', listenerError);

            // Retry logic
            if (retryCount < MAX_RETRIES) {
              const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
              console.log(`Retrying auth listener setup in ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
              retryCount++;
              setTimeout(setupAuthListener, delay);
            } else {
              setError('Failed to set up authentication listener after multiple attempts');
              setLoading(false);
            }
          }
        } else {
          console.error('Auth is not initialized');
          setError('Authentication service is not available');
          setLoading(false);
        }
      } catch (e) {
        console.error('Error in auth setup:', e);
        setError('Failed to initialize authentication');
        setLoading(false);
      }
    };

    // Start the setup process
    setupAuthListener();

    return () => {
      try {
        console.log('Cleaning up auth state listener');
        unsubscribe();
      } catch (e) {
        console.error('Error unsubscribing from auth state:', e);
      }
    };
  }, []);

  // Check if we're in fallback mode (auth not available)
  const isFallbackMode = !auth || error;

  // Context value
  const value = {
    currentUser,
    userProfile,
    loading,
    error,
    isFallbackMode,
    signup: isFallbackMode ?
      async () => ({ success: true, user: { uid: 'fallback-user' } }) :
      signup,
    login: isFallbackMode ?
      async () => ({ success: true, user: { uid: 'fallback-user' } }) :
      login,
    logout: isFallbackMode ?
      async () => ({ success: true }) :
      logout,
    resetPassword: isFallbackMode ?
      async () => ({ success: true }) :
      resetPassword,
    updateUserProfile: isFallbackMode ?
      async (data) => ({ success: true, data }) :
      updateUserProfile,
    deleteUserAccount: isFallbackMode ?
      async () => ({ success: true }) :
      deleteUserAccount,
    fetchUserProfile: isFallbackMode ?
      async () => ({ name: 'Demo User', email: '<EMAIL>' }) :
      fetchUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
