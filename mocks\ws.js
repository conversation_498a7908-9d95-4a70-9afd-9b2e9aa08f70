/**
 * Mock implementation of the ws module for React Native
 */

const EventEmitter = require('events');

class WebSocket extends EventEmitter {
  constructor(address, protocols, options) {
    super();
    
    this.readyState = WebSocket.CONNECTING;
    this.protocol = '';
    this.url = '';
    this.binaryType = 'arraybuffer';
    
    // Mock methods
    setTimeout(() => {
      this.readyState = WebSocket.CLOSED;
      this.emit('error', new Error('WebSocket is not supported in this environment'));
      this.emit('close', 1006, 'WebSocket is not supported in this environment');
    }, 0);
  }
  
  // Mock methods
  send() {
    throw new Error('WebSocket is not supported in this environment');
  }
  
  ping() {
    throw new Error('WebSocket is not supported in this environment');
  }
  
  pong() {
    throw new Error('WebSocket is not supported in this environment');
  }
  
  close() {
    this.readyState = WebSocket.CLOSING;
    setTimeout(() => {
      this.readyState = WebSocket.CLOSED;
      this.emit('close', 1000, '');
    }, 0);
  }
  
  // Event handling
  addEventListener(type, listener, options) {
    this.on(type, listener);
  }
  
  removeEventListener(type, listener, options) {
    this.off(type, listener);
  }
}

// Constants
WebSocket.CONNECTING = 0;
WebSocket.OPEN = 1;
WebSocket.CLOSING = 2;
WebSocket.CLOSED = 3;

// Static methods
WebSocket.createWebSocketStream = () => {
  throw new Error('WebSocket is not supported in this environment');
};

WebSocket.createServer = () => {
  throw new Error('WebSocket server is not supported in this environment');
};

module.exports = WebSocket;
