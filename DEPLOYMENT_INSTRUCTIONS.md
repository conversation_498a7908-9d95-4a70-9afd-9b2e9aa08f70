# Firebase Functions Deployment Instructions

This document provides instructions for deploying the Firebase Functions for the Smart AsthmaCare app.

## Prerequisites

1. Make sure you have the Firebase CLI installed:
   ```
   npm install -g firebase-tools
   ```

2. Make sure you're logged in to Firebase:
   ```
   firebase login
   ```

3. Ensure your Firebase project is properly configured in `.firebaserc`

## Deployment Steps

1. Navigate to the project root directory:
   ```
   cd Smart-AsthmaCare
   ```

2. Deploy the Firebase Functions:
   ```
   firebase deploy --only functions
   ```

   This will deploy all functions defined in `functions/index.js`.

3. If you want to deploy a specific function only:
   ```
   firebase deploy --only functions:chat
   ```

## Testing Functions Locally

1. Start the Firebase emulator:
   ```
   cd Smart-AsthmaCare
   firebase emulators:start
   ```

2. The emulator UI will be available at http://localhost:4000

## Troubleshooting

If you encounter any issues during deployment:

1. Check the Firebase CLI logs for detailed error messages
2. Verify that your `dialogflow-key.json` file is correctly placed in the `functions` directory
3. Make sure your Firebase project has the Blaze plan (pay-as-you-go) enabled, as Functions require this plan
4. Verify that the Node.js version in `functions/package.json` matches the supported version for Firebase Functions

## Function URLs

After deployment, your functions will be available at:

- Chat function: `https://us-central1-[YOUR-PROJECT-ID].cloudfunctions.net/chat`
- Peak Flow Analysis: `https://us-central1-[YOUR-PROJECT-ID].cloudfunctions.net/analyzePeakFlow`
- Medication Recommendations: `https://us-central1-[YOUR-PROJECT-ID].cloudfunctions.net/recommendMedication`

Replace `[YOUR-PROJECT-ID]` with your actual Firebase project ID (e.g., `capable-bliss-459720-t1`).

## Security Rules

Make sure to set up appropriate security rules for your Firebase project to protect your functions from unauthorized access.
