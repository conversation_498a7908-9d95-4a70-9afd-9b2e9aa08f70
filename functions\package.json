{"name": "smart-asthmacare-functions", "description": "Firebase Cloud Functions for Smart AsthmaCare App", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "echo \"No linting configured\""}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"@google-cloud/dialogflow": "^5.9.0", "cors": "^2.8.5", "firebase-admin": "^10.3.0", "firebase-functions": "^3.24.1", "uuid": "^9.0.1"}, "devDependencies": {"firebase-functions-test": "^0.2.0"}, "private": true}